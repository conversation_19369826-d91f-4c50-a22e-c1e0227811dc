from flask import Flask, render_template, request, redirect, url_for, send_from_directory
from flask_babel import Babel
from flask_frozen import Freezer
from datetime import datetime

app = Flask(__name__)
babel = Babel(app)
freezer = Freezer(app)

app.config['FREEZER_DESTINATION'] = 'build'

@app.context_processor
def inject_now():
    return {'now': datetime.now()}

@app.route('/')
def index():
    return render_template('index.html')


@app.route('/kalkulator-brutto-netto-2025')
def kalkulator_brutto_netto():
    return render_template('kalkulator-brutto-netto.html')


@app.route('/kalkulator-b2b')
def kalkulator_b2b():
    return render_template('kalkulator-b2b.html')


@app.route('/kalkulator-umowy-zlecenie')
def kalkulator_umowy_zlecenie():
    return render_template('kalkulator-umowy-zlecenie.html')


@app.route('/kalkulator-z-nadgodzinami')
def kalkulator_z_nadgodzinami():
    return render_template('kalkulator-z-nadgodzinami.html')


@app.route('/kalkulator-ppk')
def kalkulator_ppk():
    return render_template('kalkulator-ppk.html')


@app.route('/jak-obliczyc-wynagrodzenie')
def jak_obliczyc_wynagrodzenie():
    return render_template('jak-obliczyc-wynagrodzenie.html')


@app.route('/roznice-brutto-vs-netto')
def roznice_brutto_vs_netto():
    return render_template('roznice-brutto-vs-netto.html')


@app.route('/umowy-o-prace-vs-b2b')
def umowy_o_prace_vs_b2b():
    return render_template('umowy-o-prace-vs-b2b.html')


@app.route('/skladki-zus-2025')
def skladki_zus_2025():
    return render_template('skladki-zus-2025.html')


@app.route('/kontakt')
def kontakt():
    return render_template('kontakt.html')


@app.route('/polityka-prywatnosci')
def polityka_prywatnosci():
    return render_template('polityka-prywatnosci.html')


@app.errorhandler(404)
def page_not_found(e):
    return render_template('404.html'), 404


if __name__ == '__main__':
    app.run(debug=True, host='127.0.0.1', port=5000)
