{% extends 'base.html' %}

{% block title %}Jak <PERSON>ynagrodzenie - Kompletny Poradnik 2025 | Kalkulator Wynagrodzeń{% endblock %}

{% block description %}✅ Jak obliczyć wynagrodzenie krok po kroku? Kompletny poradnik 2025 z przykładami, tabelami i wzorami. Oblicz pensję brutto netto, składki ZUS i podatek.{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="#" class="text-decoration-none">Poradniki</a></li>
<li class="breadcrumb-item active" aria-current="page">Jak <PERSON>lic<PERSON> Wynagrodzenie</li>
{% endblock %}

{% block head %}
<!-- Chart.js for visualization -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block schema_data %}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "Jak Obliczyć Wynagrodzenie - Kompletny Poradnik 2025",
  "description": "Kompletny przewodnik jak obliczyć wynagrodzenie brutto netto, składki ZUS i podatek dochodowy w 2025 roku",
  "author": {
    "@type": "Organization",
    "name": "Kalkulator Wynagrodzeń",
    "url": "https://kalkulatorwynagrodzen.blog/"
  },
  "publisher": {
    "@type": "Organization",
    "name": "Kalkulator Wynagrodzeń",
    "url": "https://kalkulatorwynagrodzen.blog/"
  },
  "datePublished": "2025-01-01",
  "dateModified": "2025-01-01",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "{{ request.url }}"
  },
  "articleSection": "Poradniki",
  "keywords": "jak obliczyć wynagrodzenie, obliczanie pensji, wynagrodzenie brutto netto, składki ZUS, podatek dochodowy"
}
</script>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "HowTo",
  "name": "Jak Obliczyć Wynagrodzenie",
  "description": "Przewodnik krok po kroku jak obliczyć wynagrodzenie brutto netto",
  "step": [
    {
      "@type": "HowToStep",
      "name": "Określ wynagrodzenie brutto",
      "text": "Rozpocznij od ustalenia kwoty wynagrodzenia brutto zgodnie z umową o pracę"
    },
    {
      "@type": "HowToStep", 
      "name": "Oblicz składki ZUS",
      "text": "Odejmij składki ZUS: emerytalna (9,76%), rentowa (1,5%), chorobowa (2,45%)"
    },
    {
      "@type": "HowToStep",
      "name": "Oblicz podstawę opodatkowania", 
      "text": "Od wynagrodzenia brutto odejmij składki ZUS pracownika"
    },
    {
      "@type": "HowToStep",
      "name": "Oblicz podatek dochodowy",
      "text": "Zastosuj odpowiednią stawkę podatkową (12% lub 32%) i odejmij kwotę wolną"
    },
    {
      "@type": "HowToStep",
      "name": "Oblicz wynagrodzenie netto",
      "text": "Od podstawy opodatkowania odejmij podatek dochodowy"
    }
  ]
}
</script>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "Jak obliczyć wynagrodzenie netto z brutto?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Aby obliczyć wynagrodzenie netto z brutto, odejmij składki ZUS pracownika (13,71%) oraz podatek dochodowy od podstawy opodatkowania."
      }
    },
    {
      "@type": "Question", 
      "name": "Jakie składki ZUS odejmuje się od wynagrodzenia?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Od wynagrodzenia odejmuje się składki ZUS pracownika: emerytalna (9,76%), rentowa (1,5%), chorobowa (2,45%), co łącznie daje 13,71%."
      }
    },
    {
      "@type": "Question",
      "name": "Jak obliczyć podatek dochodowy od wynagrodzenia?",
      "acceptedAnswer": {
        "@type": "Answer", 
        "text": "Podatek dochodowy oblicza się od podstawy opodatkowania (brutto minus składki ZUS) według stawki 12% do 120 000 zł rocznie, powyżej tej kwoty 32%."
      }
    }
  ]
}
</script>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="bg-primary text-white py-5">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-8">
        <h1 class="display-5 fw-bold mb-4">Jak Obliczyć Wynagrodzenie - Kompletny Poradnik 2025</h1>
        <p class="lead mb-4">Dowiedz się <strong>jak obliczyć wynagrodzenie</strong> krok po kroku. Poznaj wzory, przykłady i praktyczne wskazówki do obliczania pensji brutto netto, składek ZUS i podatku dochodowego w 2025 roku.</p>
        <div class="d-flex gap-3 flex-wrap">
          <a href="#kalkulator-sekcja" class="btn btn-light btn-lg">
            <i class="fas fa-calculator me-2"></i>Skorzystaj z Kalkulatora
          </a>
          <a href="#wzory" class="btn btn-outline-light btn-lg">
            <i class="fas fa-formula me-2"></i>Zobacz Wzory
          </a>
        </div>
      </div>
      <div class="col-lg-4 text-center">
        <div class="bg-white bg-opacity-10 rounded-3 p-4">
          <i class="fas fa-chart-line fa-4x mb-3"></i>
          <h5>Aktualne na 2025 rok</h5>
          <p class="mb-0">Wszystkie stawki i przepisy</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Quick Calculator Section -->
<section id="kalkulator-sekcja" class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Szybki Kalkulator Wynagrodzeń</h2>
      <p class="lead">Sprawdź <strong>jak obliczyć wynagrodzenie</strong> używając naszego darmowego kalkulatora</p>
    </div>
    
    <div class="row justify-content-center">
      <div class="col-lg-8">
        <div class="card shadow">
          <div class="card-body p-4">
            <form id="quick-salary-calc">
              <div class="row g-3 mb-4">
                <div class="col-md-6">
                  <label for="gross-amount" class="form-label fw-bold">Wynagrodzenie brutto (PLN)</label>
                  <input type="number" id="gross-amount" class="form-control form-control-lg" placeholder="5000" min="0" step="0.01">
                </div>
                <div class="col-md-6">
                  <label for="contract-type-quick" class="form-label fw-bold">Typ umowy</label>
                  <select id="contract-type-quick" class="form-select form-select-lg">
                    <option value="employment">Umowa o pracę</option>
                    <option value="mandate">Umowa zlecenie</option>
                    <option value="b2b">Umowa B2B</option>
                  </select>
                </div>
              </div>
              
              <div class="text-center">
                <button type="submit" class="btn btn-primary btn-lg px-5">
                  <i class="fas fa-calculator me-2"></i>Oblicz Wynagrodzenie
                </button>
              </div>
            </form>
            
            <div id="quick-results" class="mt-4" style="display: none;">
              <div class="row g-3">
                <div class="col-md-4">
                  <div class="text-center p-3 bg-primary text-white rounded">
                    <h6 class="mb-1">Brutto</h6>
                    <div class="h5 mb-0"><span id="result-gross">--</span> PLN</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="text-center p-3 bg-success text-white rounded">
                    <h6 class="mb-1">Netto</h6>
                    <div class="h5 mb-0"><span id="result-net">--</span> PLN</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="text-center p-3 bg-info text-white rounded">
                    <h6 class="mb-1">Składki ZUS</h6>
                    <div class="h5 mb-0"><span id="result-zus">--</span> PLN</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Main Content -->
<section class="py-5">
  <div class="container">
    <div class="row">
      <div class="col-lg-8 mx-auto">
        
        <!-- Introduction -->
        <div class="mb-5">
          <h2 class="fw-bold mb-4">Co to Znaczy Obliczyć Wynagrodzenie?</h2>
          <p class="lead mb-4"><strong>Jak obliczyć wynagrodzenie</strong> to podstawowa umiejętność każdego pracownika i pracodawcy. Obliczanie wynagrodzenia polega na przekształceniu kwoty brutto (określonej w umowie) na kwotę netto (którą otrzymujesz na konto), uwzględniając wszystkie obowiązkowe składki i podatki.</p>
          
          <div class="alert alert-info">
            <h5 class="alert-heading"><i class="fas fa-info-circle me-2"></i>Ważne!</h5>
            <p class="mb-0">W 2025 roku obowiązują nowe stawki składek ZUS i kwoty wolne od podatku. Nasz poradnik <strong>jak obliczyć wynagrodzenie</strong> uwzględnia wszystkie aktualne przepisy.</p>
          </div>
          
          <p>Proces obliczania wynagrodzenia składa się z kilku etapów:</p>
          <ul class="list-unstyled">
            <li class="mb-2"><i class="fas fa-check text-success me-2"></i><strong>Ustalenie wynagrodzenia brutto</strong> - kwota określona w umowie</li>
            <li class="mb-2"><i class="fas fa-check text-success me-2"></i><strong>Obliczenie składek ZUS</strong> - emerytalna, rentowa, chorobowa</li>
            <li class="mb-2"><i class="fas fa-check text-success me-2"></i><strong>Określenie podstawy opodatkowania</strong> - brutto minus składki ZUS</li>
            <li class="mb-2"><i class="fas fa-check text-success me-2"></i><strong>Obliczenie podatku dochodowego</strong> - według obowiązującej stawki</li>
            <li class="mb-2"><i class="fas fa-check text-success me-2"></i><strong>Ustalenie wynagrodzenia netto</strong> - końcowa kwota do wypłaty</li>
          </ul>
        </div>

        <!-- Step by Step Guide -->
        <div class="mb-5" id="wzory">
          <h2 class="fw-bold mb-4">Jak Obliczyć Wynagrodzenie Krok Po Kroku</h2>
          
          <div class="row g-4">
            <div class="col-12">
              <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                  <h5 class="mb-0"><i class="fas fa-step-forward me-2"></i>Krok 1: Ustal Wynagrodzenie Brutto</h5>
                </div>
                <div class="card-body">
                  <p><strong>Wynagrodzenie brutto</strong> to kwota określona w umowie o pracę przed odliczeniem jakichkolwiek składek czy podatków. To punkt wyjścia do wszystkich obliczeń.</p>
                  <div class="bg-light p-3 rounded">
                    <strong>Przykład:</strong> Umowa przewiduje wynagrodzenie 5 000 PLN brutto miesięcznie.
                  </div>
                </div>
              </div>
            </div>
            
            <div class="col-12">
              <div class="card border-info">
                <div class="card-header bg-info text-white">
                  <h5 class="mb-0"><i class="fas fa-step-forward me-2"></i>Krok 2: Oblicz Składki ZUS Pracownika</h5>
                </div>
                <div class="card-body">
                  <p>Od wynagrodzenia brutto odejmujemy składki ZUS pracownika. W 2025 roku obowiązują następujące stawki:</p>
                  
                  <div class="table-responsive">
                    <table class="table table-striped">
                      <thead>
                        <tr>
                          <th>Składka</th>
                          <th>Stawka 2025</th>
                          <th>Przykład (5000 PLN)</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>Emerytalna</td>
                          <td>9,76%</td>
                          <td>488,00 PLN</td>
                        </tr>
                        <tr>
                          <td>Rentowa</td>
                          <td>1,50%</td>
                          <td>75,00 PLN</td>
                        </tr>
                        <tr>
                          <td>Chorobowa</td>
                          <td>2,45%</td>
                          <td>122,50 PLN</td>
                        </tr>
                        <tr class="table-primary">
                          <td><strong>RAZEM</strong></td>
                          <td><strong>13,71%</strong></td>
                          <td><strong>685,50 PLN</strong></td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  
                  <div class="bg-light p-3 rounded mt-3">
                    <strong>Wzór:</strong> Składki ZUS = Wynagrodzenie brutto × 13,71%<br>
                    <strong>Obliczenie:</strong> 5 000 PLN × 13,71% = 685,50 PLN
                  </div>
                </div>
              </div>
            </div>

            <div class="col-12">
              <div class="card border-success">
                <div class="card-header bg-success text-white">
                  <h5 class="mb-0"><i class="fas fa-step-forward me-2"></i>Krok 3: Oblicz Podstawę Opodatkowania</h5>
                </div>
                <div class="card-body">
                  <p>Podstawa opodatkowania to kwota, od której obliczamy podatek dochodowy. Otrzymujemy ją odejmując składki ZUS od wynagrodzenia brutto.</p>

                  <div class="bg-light p-3 rounded">
                    <strong>Wzór:</strong> Podstawa opodatkowania = Wynagrodzenie brutto - Składki ZUS<br>
                    <strong>Obliczenie:</strong> 5 000 PLN - 685,50 PLN = 4 314,50 PLN
                  </div>
                </div>
              </div>
            </div>

            <div class="col-12">
              <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                  <h5 class="mb-0"><i class="fas fa-step-forward me-2"></i>Krok 4: Oblicz Podatek Dochodowy</h5>
                </div>
                <div class="card-body">
                  <p>W 2025 roku obowiązuje progresywna skala podatkowa z dwiema stawkami:</p>

                  <div class="table-responsive">
                    <table class="table table-bordered">
                      <thead class="table-dark">
                        <tr>
                          <th>Dochód roczny</th>
                          <th>Stawka podatku</th>
                          <th>Kwota wolna</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>Do 120 000 PLN</td>
                          <td>12%</td>
                          <td>3 600 PLN rocznie (300 PLN miesięcznie)</td>
                        </tr>
                        <tr>
                          <td>Powyżej 120 000 PLN</td>
                          <td>32%</td>
                          <td>Brak</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>

                  <div class="bg-light p-3 rounded">
                    <strong>Wzór dla pierwszego progu:</strong> Podatek = (Podstawa opodatkowania × 12%) - 300 PLN<br>
                    <strong>Obliczenie:</strong> (4 314,50 PLN × 12%) - 300 PLN = 517,74 PLN - 300 PLN = 217,74 PLN
                  </div>
                </div>
              </div>
            </div>

            <div class="col-12">
              <div class="card border-dark">
                <div class="card-header bg-dark text-white">
                  <h5 class="mb-0"><i class="fas fa-step-forward me-2"></i>Krok 5: Oblicz Wynagrodzenie Netto</h5>
                </div>
                <div class="card-body">
                  <p>Wynagrodzenie netto to końcowa kwota, którą otrzymasz na konto po odliczeniu wszystkich składek i podatków.</p>

                  <div class="bg-light p-3 rounded mb-3">
                    <strong>Wzór:</strong> Wynagrodzenie netto = Podstawa opodatkowania - Podatek dochodowy<br>
                    <strong>Obliczenie:</strong> 4 314,50 PLN - 217,74 PLN = 4 096,76 PLN
                  </div>

                  <div class="alert alert-success">
                    <h6 class="alert-heading">Podsumowanie przykładu:</h6>
                    <ul class="mb-0">
                      <li>Wynagrodzenie brutto: <strong>5 000,00 PLN</strong></li>
                      <li>Składki ZUS: <strong>685,50 PLN</strong></li>
                      <li>Podatek dochodowy: <strong>217,74 PLN</strong></li>
                      <li>Wynagrodzenie netto: <strong>4 096,76 PLN</strong></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Salary Breakdown Chart -->
        <div class="mb-5">
          <h2 class="fw-bold mb-4">Wizualizacja Podziału Wynagrodzenia</h2>
          <div class="card">
            <div class="card-body">
              <div class="row">
                <div class="col-lg-8">
                  <canvas id="salaryBreakdownChart" width="400" height="200"></canvas>
                </div>
                <div class="col-lg-4">
                  <h5 class="mb-3">Legenda</h5>
                  <ul class="list-unstyled">
                    <li class="mb-2"><span class="badge bg-success me-2">■</span>Wynagrodzenie netto (81,9%)</li>
                    <li class="mb-2"><span class="badge bg-primary me-2">■</span>Składki ZUS (13,7%)</li>
                    <li class="mb-2"><span class="badge bg-warning me-2">■</span>Podatek dochodowy (4,4%)</li>
                  </ul>
                  <div class="mt-4">
                    <small class="text-muted">Wykres pokazuje procentowy podział wynagrodzenia brutto 5 000 PLN na poszczególne składniki.</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Different Contract Types -->
        <div class="mb-5">
          <h2 class="fw-bold mb-4">Jak Obliczyć Wynagrodzenie dla Różnych Typów Umów</h2>

          <div class="row g-4">
            <div class="col-md-6">
              <div class="card h-100">
                <div class="card-header bg-primary text-white">
                  <h5 class="mb-0"><i class="fas fa-file-contract me-2"></i>Umowa o Pracę</h5>
                </div>
                <div class="card-body">
                  <p><strong>Jak obliczyć wynagrodzenie</strong> na umowie o pracę:</p>
                  <ul class="list-unstyled">
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Składki ZUS: 13,71%</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Podatek: 12% lub 32%</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Kwota wolna: 300 PLN/miesiąc</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>PPK: opcjonalnie 2%</li>
                  </ul>
                  <div class="bg-light p-2 rounded">
                    <small><strong>Przykład:</strong> 5000 PLN brutto = 4096,76 PLN netto</small>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-md-6">
              <div class="card h-100">
                <div class="card-header bg-info text-white">
                  <h5 class="mb-0"><i class="fas fa-handshake me-2"></i>Umowa Zlecenie</h5>
                </div>
                <div class="card-body">
                  <p><strong>Jak obliczyć wynagrodzenie</strong> na umowie zlecenie:</p>
                  <ul class="list-unstyled">
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Składki ZUS: 13,71% (jeśli obowiązkowe)</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Podatek: 12% lub 32%</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Kwota wolna: 300 PLN/miesiąc</li>
                    <li class="mb-2"><i class="fas fa-times text-danger me-2"></i>Brak składki chorobowej</li>
                  </ul>
                  <div class="bg-light p-2 rounded">
                    <small><strong>Przykład:</strong> 5000 PLN brutto = 4219,26 PLN netto</small>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-md-6">
              <div class="card h-100">
                <div class="card-header bg-success text-white">
                  <h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>Umowa B2B</h5>
                </div>
                <div class="card-body">
                  <p><strong>Jak obliczyć wynagrodzenie</strong> B2B:</p>
                  <ul class="list-unstyled">
                    <li class="mb-2"><i class="fas fa-times text-danger me-2"></i>Brak składek ZUS (opcjonalnie)</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Podatek: 19% (liniowy) lub 12%/32%</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Koszty uzyskania: 20% lub rzeczywiste</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>VAT: 23% (jeśli dotyczy)</li>
                  </ul>
                  <div class="bg-light p-2 rounded">
                    <small><strong>Przykład:</strong> 5000 PLN netto = ~6150 PLN brutto</small>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-md-6">
              <div class="card h-100">
                <div class="card-header bg-warning text-dark">
                  <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>Umowa o Dzieło</h5>
                </div>
                <div class="card-body">
                  <p><strong>Jak obliczyć wynagrodzenie</strong> za dzieło:</p>
                  <ul class="list-unstyled">
                    <li class="mb-2"><i class="fas fa-times text-danger me-2"></i>Brak składek ZUS</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Podatek: 12% lub 32%</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Kwota wolna: 300 PLN/miesiąc</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Koszty: 20% lub rzeczywiste</li>
                  </ul>
                  <div class="bg-light p-2 rounded">
                    <small><strong>Przykład:</strong> 5000 PLN brutto = 4780 PLN netto</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Comparison Table -->
        <div class="mb-5">
          <h2 class="fw-bold mb-4">Porównanie Wynagrodzeń dla Różnych Umów</h2>
          <div class="table-responsive">
            <table class="table table-striped table-hover">
              <thead class="table-dark">
                <tr>
                  <th>Typ umowy</th>
                  <th>Brutto (PLN)</th>
                  <th>Składki ZUS (PLN)</th>
                  <th>Podatek (PLN)</th>
                  <th>Netto (PLN)</th>
                  <th>% netto z brutto</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><strong>Umowa o pracę</strong></td>
                  <td>5 000</td>
                  <td>685,50</td>
                  <td>217,74</td>
                  <td><strong>4 096,76</strong></td>
                  <td>81,9%</td>
                </tr>
                <tr>
                  <td><strong>Umowa zlecenie</strong></td>
                  <td>5 000</td>
                  <td>563,00</td>
                  <td>217,74</td>
                  <td><strong>4 219,26</strong></td>
                  <td>84,4%</td>
                </tr>
                <tr>
                  <td><strong>Umowa B2B (19%)</strong></td>
                  <td>5 000</td>
                  <td>0</td>
                  <td>760,00</td>
                  <td><strong>4 240,00</strong></td>
                  <td>84,8%</td>
                </tr>
                <tr>
                  <td><strong>Umowa o dzieło</strong></td>
                  <td>5 000</td>
                  <td>0</td>
                  <td>220,00</td>
                  <td><strong>4 780,00</strong></td>
                  <td>95,6%</td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="alert alert-info mt-3">
            <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i>Uwaga</h6>
            <p class="mb-0">Powyższe obliczenia są przykładowe i mogą się różnić w zależności od indywidualnej sytuacji. Zawsze skonsultuj się z księgowym w sprawach podatkowych.</p>
          </div>
        </div>

        <!-- Practical Tips -->
        <div class="mb-5">
          <h2 class="fw-bold mb-4">Praktyczne Wskazówki - Jak Obliczyć Wynagrodzenie Efektywnie</h2>

          <div class="row g-4">
            <div class="col-md-6">
              <div class="card border-success h-100">
                <div class="card-body">
                  <h5 class="card-title text-success"><i class="fas fa-lightbulb me-2"></i>Wskazówki dla Pracowników</h5>
                  <ul class="list-unstyled">
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Zawsze sprawdzaj pasek wypłaty</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Używaj kalkulatora wynagrodzeń do weryfikacji</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Pamiętaj o kwotach wolnych od podatku</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Rozważ uczestnictwo w PPK</li>
                    <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Śledź zmiany w przepisach</li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="col-md-6">
              <div class="card border-primary h-100">
                <div class="card-body">
                  <h5 class="card-title text-primary"><i class="fas fa-building me-2"></i>Wskazówki dla Pracodawców</h5>
                  <ul class="list-unstyled">
                    <li class="mb-2"><i class="fas fa-check text-primary me-2"></i>Planuj budżet na wynagrodzenia z wyprzedzeniem</li>
                    <li class="mb-2"><i class="fas fa-check text-primary me-2"></i>Uwzględniaj koszty pracodawcy (składki ZUS)</li>
                    <li class="mb-2"><i class="fas fa-check text-primary me-2"></i>Porównuj koszty różnych typów umów</li>
                    <li class="mb-2"><i class="fas fa-check text-primary me-2"></i>Korzystaj z profesjonalnych systemów HR</li>
                    <li class="mb-2"><i class="fas fa-check text-primary me-2"></i>Konsultuj się z księgowym</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- FAQ Section -->
        <div class="mb-5">
          <h2 class="fw-bold mb-4">Często Zadawane Pytania - Jak Obliczyć Wynagrodzenie</h2>

          <div class="accordion" id="faqAccordion">
            <div class="accordion-item">
              <h3 class="accordion-header" id="faq1">
                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                  <strong>Jak obliczyć wynagrodzenie netto z brutto?</strong>
                </button>
              </h3>
              <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                <div class="accordion-body">
                  <p>Aby <strong>obliczyć wynagrodzenie netto z brutto</strong>, wykonaj następujące kroki:</p>
                  <ol>
                    <li>Od wynagrodzenia brutto odejmij składki ZUS pracownika (13,71%)</li>
                    <li>Oblicz podstawę opodatkowania</li>
                    <li>Oblicz podatek dochodowy (12% lub 32% minus kwota wolna)</li>
                    <li>Od podstawy opodatkowania odejmij podatek</li>
                  </ol>
                  <p><strong>Przykład:</strong> 5000 PLN brutto = 4096,76 PLN netto (umowa o pracę)</p>
                </div>
              </div>
            </div>

            <div class="accordion-item">
              <h3 class="accordion-header" id="faq2">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                  <strong>Jakie składki ZUS odejmuje się od wynagrodzenia?</strong>
                </button>
              </h3>
              <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                <div class="accordion-body">
                  <p>Od wynagrodzenia brutto odejmuje się następujące składki ZUS pracownika:</p>
                  <ul>
                    <li><strong>Składka emerytalna:</strong> 9,76%</li>
                    <li><strong>Składka rentowa:</strong> 1,50%</li>
                    <li><strong>Składka chorobowa:</strong> 2,45%</li>
                  </ul>
                  <p><strong>Łącznie:</strong> 13,71% wynagrodzenia brutto</p>
                  <p>Dodatkowo pracodawca płaci składki pracodawcy, które nie wpływają na wynagrodzenie netto pracownika.</p>
                </div>
              </div>
            </div>

            <div class="accordion-item">
              <h3 class="accordion-header" id="faq3">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                  <strong>Jak obliczyć podatek dochodowy od wynagrodzenia?</strong>
                </button>
              </h3>
              <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                <div class="accordion-body">
                  <p>Podatek dochodowy oblicza się od podstawy opodatkowania (brutto minus składki ZUS) według progresywnej skali:</p>
                  <ul>
                    <li><strong>Do 120 000 PLN rocznie:</strong> 12% minus 300 PLN miesięcznie (kwota wolna)</li>
                    <li><strong>Powyżej 120 000 PLN rocznie:</strong> 32% (bez kwoty wolnej)</li>
                  </ul>
                  <p><strong>Wzór:</strong> Podatek = (Podstawa opodatkowania × stawka) - kwota wolna</p>
                </div>
              </div>
            </div>

            <div class="accordion-item">
              <h3 class="accordion-header" id="faq4">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4">
                  <strong>Czy można obliczyć wynagrodzenie brutto z netto?</strong>
                </button>
              </h3>
              <div id="collapse4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                <div class="accordion-body">
                  <p>Tak, można <strong>obliczyć wynagrodzenie brutto z netto</strong>, ale wymaga to bardziej skomplikowanych obliczeń:</p>
                  <ol>
                    <li>Ustal przybliżoną kwotę brutto</li>
                    <li>Oblicz składki ZUS i podatek</li>
                    <li>Sprawdź czy wynik netto się zgadza</li>
                    <li>Dostosuj kwotę brutto i powtórz obliczenia</li>
                  </ol>
                  <p>Najłatwiej skorzystać z kalkulatora wynagrodzeń, który automatycznie wykona te obliczenia.</p>
                </div>
              </div>
            </div>

            <div class="accordion-item">
              <h3 class="accordion-header" id="faq5">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse5">
                  <strong>Jak obliczyć wynagrodzenie z nadgodzinami?</strong>
                </button>
              </h3>
              <div id="collapse5" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                <div class="accordion-body">
                  <p>Przy obliczaniu wynagrodzenia z nadgodzinami:</p>
                  <ul>
                    <li><strong>Nadgodziny w dni robocze:</strong> +50% stawki godzinowej</li>
                    <li><strong>Nadgodziny w nocy, święta, niedziele:</strong> +100% stawki godzinowej</li>
                  </ul>
                  <p><strong>Przykład:</strong> Stawka godzinowa 30 PLN, 10 nadgodzin = 30 PLN × 10h × 1,5 = 450 PLN dodatku</p>
                  <p>Całkowite wynagrodzenie (podstawa + nadgodziny) podlega standardowym składkom ZUS i podatkowi.</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Tools and Resources -->
        <div class="mb-5">
          <h2 class="fw-bold mb-4">Przydatne Narzędzia do Obliczania Wynagrodzeń</h2>

          <div class="row g-4">
            <div class="col-md-4">
              <div class="card text-center h-100">
                <div class="card-body">
                  <i class="fas fa-calculator fa-3x text-primary mb-3"></i>
                  <h5 class="card-title">Kalkulator Wynagrodzeń</h5>
                  <p class="card-text">Darmowy kalkulator do szybkiego obliczania wynagrodzenia brutto netto</p>
                  <a href="/" class="btn btn-primary">Skorzystaj</a>
                </div>
              </div>
            </div>

            <div class="col-md-4">
              <div class="card text-center h-100">
                <div class="card-body">
                  <i class="fas fa-briefcase fa-3x text-success mb-3"></i>
                  <h5 class="card-title">Kalkulator B2B</h5>
                  <p class="card-text">Specjalistyczny kalkulator dla umów B2B i działalności gospodarczej</p>
                  <a href="/kalkulator-b2b" class="btn btn-success">Oblicz B2B</a>
                </div>
              </div>
            </div>

            <div class="col-md-4">
              <div class="card text-center h-100">
                <div class="card-body">
                  <i class="fas fa-clock fa-3x text-warning mb-3"></i>
                  <h5 class="card-title">Kalkulator z Nadgodzinami</h5>
                  <p class="card-text">Oblicz wynagrodzenie uwzględniając dodatki za nadgodziny</p>
                  <a href="/kalkulator-z-nadgodzinami" class="btn btn-warning">Oblicz</a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Summary -->
        <div class="mb-5">
          <div class="card bg-primary text-white">
            <div class="card-body">
              <h2 class="card-title fw-bold">Podsumowanie - Jak Obliczyć Wynagrodzenie</h2>
              <p class="card-text lead">Obliczanie wynagrodzenia to proces składający się z kilku etapów. Najważniejsze to zrozumienie różnicy między wynagrodzeniem brutto a netto oraz znajomość aktualnych stawek składek ZUS i podatków.</p>

              <div class="row g-3 mt-3">
                <div class="col-md-6">
                  <h5><i class="fas fa-key me-2"></i>Kluczowe elementy:</h5>
                  <ul class="list-unstyled">
                    <li><i class="fas fa-check me-2"></i>Wynagrodzenie brutto (umowa)</li>
                    <li><i class="fas fa-check me-2"></i>Składki ZUS (13,71%)</li>
                    <li><i class="fas fa-check me-2"></i>Podatek dochodowy (12% lub 32%)</li>
                    <li><i class="fas fa-check me-2"></i>Kwota wolna (300 PLN/miesiąc)</li>
                  </ul>
                </div>
                <div class="col-md-6">
                  <h5><i class="fas fa-tools me-2"></i>Przydatne narzędzia:</h5>
                  <ul class="list-unstyled">
                    <li><i class="fas fa-check me-2"></i>Kalkulator wynagrodzeń online</li>
                    <li><i class="fas fa-check me-2"></i>Aplikacje mobilne</li>
                    <li><i class="fas fa-check me-2"></i>Arkusze kalkulacyjne</li>
                    <li><i class="fas fa-check me-2"></i>Konsultacje z księgowym</li>
                  </ul>
                </div>
              </div>

              <div class="mt-4 text-center">
                <a href="/" class="btn btn-light btn-lg me-3">
                  <i class="fas fa-calculator me-2"></i>Skorzystaj z Kalkulatora
                </a>
                <a href="#" class="btn btn-outline-light btn-lg">
                  <i class="fas fa-download me-2"></i>Pobierz Poradnik PDF
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Quick Calculator
    const form = document.getElementById('quick-salary-calc');
    const resultsDiv = document.getElementById('quick-results');

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const grossAmount = parseFloat(document.getElementById('gross-amount').value);
        const contractType = document.getElementById('contract-type-quick').value;

        if (!grossAmount || grossAmount <= 0) {
            alert('Wprowadź prawidłową kwotę wynagrodzenia brutto');
            return;
        }

        let zusContributions = 0;
        let tax = 0;
        let netAmount = 0;

        // Calculate based on contract type
        switch(contractType) {
            case 'employment':
                zusContributions = grossAmount * 0.1371; // 13.71%
                const taxBase = grossAmount - zusContributions;
                tax = Math.max(0, (taxBase * 0.12) - 300); // 12% tax rate, 300 PLN tax-free amount
                netAmount = taxBase - tax;
                break;

            case 'mandate':
                zusContributions = grossAmount * 0.1126; // 11.26% (no sickness insurance)
                const taxBaseMand = grossAmount - zusContributions;
                tax = Math.max(0, (taxBaseMand * 0.12) - 300);
                netAmount = taxBaseMand - tax;
                break;

            case 'b2b':
                zusContributions = 0; // No ZUS for B2B
                const costs = grossAmount * 0.20; // 20% costs
                const taxableIncome = grossAmount - costs;
                tax = taxableIncome * 0.19; // 19% linear tax
                netAmount = grossAmount - tax;
                break;
        }

        document.getElementById('result-gross').textContent = grossAmount.toFixed(2);
        document.getElementById('result-net').textContent = netAmount.toFixed(2);
        document.getElementById('result-zus').textContent = zusContributions.toFixed(2);

        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });

    // Salary Breakdown Chart
    const ctx = document.getElementById('salaryBreakdownChart');
    if (ctx) {
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Wynagrodzenie netto', 'Składki ZUS', 'Podatek dochodowy'],
                datasets: [{
                    data: [4096.76, 685.50, 217.74],
                    backgroundColor: [
                        '#28a745', // Green for net salary
                        '#007bff', // Blue for ZUS
                        '#ffc107'  // Yellow for tax
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const percentage = ((value / 5000) * 100).toFixed(1);
                                return `${label}: ${value.toFixed(2)} PLN (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add animation to cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe all cards
    document.querySelectorAll('.card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});
</script>

<style>
/* Custom styles for better visual appeal */
.hero-section {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.accordion-button:not(.collapsed) {
    background-color: #e3f2fd;
    color: #1976d2;
}

.table-hover tbody tr:hover {
    background-color: #f8f9fa;
}

.badge {
    font-size: 0.9em;
}

/* Chart container styling */
.chart-container {
    position: relative;
    height: 300px;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .display-5 {
        font-size: 2rem;
    }

    .lead {
        font-size: 1.1rem;
    }

    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
}

/* Print styles */
@media print {
    .btn, .navbar, .breadcrumb, footer {
        display: none !important;
    }

    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
