{% extends 'base.html' %}

{% block head %}
<!-- Chart.js for salary breakdown visualization -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block title %}Kalkulator Brutto Netto 2025 - Oblicz Wynagrodzenie | Darmowy{% endblock %}

{% block description %}✅ Kalkulator brutto netto 2025 - oblicz wynagrodzenie brutto na netto i odwrotnie. Darmowy kalkulator wynagrodzeń z aktualną stawką składek ZUS i podatków. Sprawdź teraz!{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active" aria-current="page">Kalkulator Brutto-Netto 2025</li>
{% endblock %}

{% block schema_data %}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": ["WebApplication", "Calculator"],
  "name": "Kalkulator Brutto Netto 2025",
  "url": "{{ request.url }}",
  "description": "Darmowy kalkulator brutto netto 2025 - oblicz wynagrodzenie brutto na netto i odwrotnie z aktualną stawką składek ZUS i podatków",
  "applicationCategory": "FinanceApplication",
  "operatingSystem": "All",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "PLN"
  },
  "author": {
    "@type": "Organization",
    "name": "Kalkulator Wynagrodzeń",
    "url": "https://kalkulatorwynagrodzen.blog/"
  },
  "potentialAction": {
    "@type": "UseAction",
    "target": "{{ request.url }}#kalkulator-brutto-netto",
    "object": {
      "@type": "WebApplication",
      "name": "Kalkulator Brutto Netto"
    }
  }
}
</script>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "Jak obliczyć wynagrodzenie brutto na netto?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Aby obliczyć wynagrodzenie brutto na netto, należy od kwoty brutto odjąć składki ZUS pracownika (13,71%), podatek dochodowy oraz ewentualnie składkę PPK (2%). Nasz kalkulator brutto netto automatycznie wykonuje te obliczenia."
      }
    },
    {
      "@type": "Question",
      "name": "Jakie są aktualne stawki składek ZUS w 2025 roku?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "W 2025 roku składki ZUS pracownika wynoszą łącznie 13,71%: emerytalna 9,76%, rentowa 1,5%, chorobowa 2,45%. Dodatkowo pracodawca płaci składki w wysokości około 19,52%."
      }
    }
  ]
}
</script>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section bg-primary text-white text-center py-5" id="hero">
  <div class="container">
    <h1 class="display-4 fw-bold mb-4">Kalkulator Brutto Netto 2025 - Oblicz Wynagrodzenie</h1>
    <p class="lead mb-5">Darmowy <strong>kalkulator brutto netto</strong> 2025 - oblicz wynagrodzenie brutto na netto i odwrotnie z aktualną stawką składek ZUS i podatków</p>
    <div class="d-flex justify-content-center gap-3 flex-wrap">
      <a href="#kalkulator-brutto-netto" class="btn btn-light btn-lg px-4 py-2">
        <i class="fas fa-calculator me-2"></i>Oblicz Brutto-Netto
      </a>
      <a href="#poradnik-brutto-netto" class="btn btn-outline-light btn-lg px-4 py-2">
        <i class="fas fa-book me-2"></i>Jak Obliczyć
      </a>
    </div>
  </div>
</section>

<!-- Kalkulator Brutto-Netto Section -->
<section id="kalkulator-brutto-netto" class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Kalkulator Brutto Netto 2025 - Oblicz Wynagrodzenie</h2>
      <p class="lead">Nasz <strong>kalkulator wynagrodzeń brutto netto</strong> pozwala szybko obliczyć wynagrodzenie brutto na netto i odwrotnie z uwzględnieniem aktualnych stawek składek ZUS i podatków na 2025 rok.</p>
    </div>

    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="card shadow-sm">
          <div class="card-body p-4">
            <form id="brutto-netto-calculator">
              <div class="row g-3 mb-4">
                <div class="col-md-6">
                  <label for="contract-type" class="form-label fw-bold">Typ umowy</label>
                  <select id="contract-type" class="form-select" required>
                    <option value="">Wybierz typ umowy</option>
                    <option value="employment" selected>Umowa o pracę</option>
                    <option value="mandate">Umowa zlecenie</option>
                    <option value="b2b">Umowa B2B</option>
                    <option value="task">Umowa o dzieło</option>
                  </select>
                </div>

                <div class="col-md-6">
                  <label for="tax-year" class="form-label fw-bold">Rok podatkowy</label>
                  <select id="tax-year" class="form-select">
                    <option value="2025" selected>2025</option>
                  </select>
                </div>
              </div>

              <div class="row g-3 mb-4">
                <div class="col-md-6">
                  <label for="salary-amount" class="form-label fw-bold">Kwota wynagrodzenia</label>
                  <div class="input-group">
                    <input type="number" id="salary-amount" class="form-control" placeholder="Wprowadź kwotę" min="0" step="0.01" required>
                    <span class="input-group-text">PLN</span>
                  </div>
                </div>

                <div class="col-md-6">
                  <label class="form-label fw-bold">Typ kwoty</label>
                  <div class="d-flex gap-3 mt-2">
                    <div class="form-check">
                      <input class="form-check-input" type="radio" name="amount-type" id="gross" value="gross" checked>
                      <label class="form-check-label" for="gross">
                        <strong>Brutto</strong> <small class="text-muted">(przed odliczeniami)</small>
                      </label>
                    </div>
                    <div class="form-check">
                      <input class="form-check-input" type="radio" name="amount-type" id="net" value="net">
                      <label class="form-check-label" for="net">
                        <strong>Netto</strong> <small class="text-muted">(na rękę)</small>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <div class="row g-3 mb-4">
                <div class="col-md-4">
                  <label for="age" class="form-label fw-bold">Wiek</label>
                  <div class="input-group">
                    <input type="number" id="age" class="form-control" placeholder="26" min="18" max="100" value="26">
                    <span class="input-group-text">lat</span>
                  </div>
                </div>

                <div class="col-md-4">
                  <label for="location" class="form-label fw-bold">Miejsce pracy</label>
                  <select id="location" class="form-select">
                    <option value="other">Inne miasta</option>
                    <option value="warszawa">Warszawa</option>
                    <option value="krakow">Kraków</option>
                    <option value="gdansk">Gdańsk</option>
                    <option value="wroclaw">Wrocław</option>
                  </select>
                </div>

                <div class="col-md-4">
                  <div class="form-check mt-4">
                    <input class="form-check-input" type="checkbox" id="ppk" value="ppk">
                    <label class="form-check-label fw-bold" for="ppk">
                      Uczestniczę w PPK
                      <small class="d-block text-muted">Pracownicze Plany Kapitałowe</small>
                    </label>
                  </div>
                </div>
              </div>

              <div class="text-center">
                <button type="submit" class="btn btn-primary btn-lg px-5 py-3">
                  <i class="fas fa-calculator me-2"></i>OBLICZ BRUTTO-NETTO
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- Results Section -->
        <div id="brutto-netto-results" class="mt-4" style="display: none;">
          <div class="card border-success">
            <div class="card-header bg-success text-white">
              <h4 class="mb-0"><i class="fas fa-chart-line me-2"></i>Wyniki Kalkulacji Brutto-Netto</h4>
            </div>
            <div class="card-body">
              <div class="row g-4">
                <div class="col-md-6">
                  <div class="result-item p-3 bg-light rounded">
                    <h5 class="text-primary mb-2"><i class="fas fa-money-bill-wave me-2"></i>Wynagrodzenie brutto</h5>
                    <div class="h3 text-dark mb-0"><span id="gross-result">--</span> PLN</div>
                    <small class="text-muted">Kwota przed odliczeniami</small>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="result-item p-3 bg-light rounded">
                    <h5 class="text-success mb-2"><i class="fas fa-hand-holding-usd me-2"></i>Wynagrodzenie netto</h5>
                    <div class="h3 text-dark mb-0"><span id="net-result">--</span> PLN</div>
                    <small class="text-muted">Kwota na rękę</small>
                  </div>
                </div>
              </div>

              <div class="row g-4 mt-2">
                <div class="col-md-4">
                  <div class="result-item p-3 border rounded">
                    <h6 class="text-muted mb-2"><i class="fas fa-shield-alt me-2"></i>Składki ZUS</h6>
                    <div class="h5 mb-0"><span id="zus-result">--</span> PLN</div>
                    <small class="text-muted">13,71% od brutto</small>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="result-item p-3 border rounded">
                    <h6 class="text-muted mb-2"><i class="fas fa-percentage me-2"></i>Podatek dochodowy</h6>
                    <div class="h5 mb-0"><span id="tax-result">--</span> PLN</div>
                    <small class="text-muted">12% lub 32%</small>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="result-item p-3 border rounded">
                    <h6 class="text-muted mb-2"><i class="fas fa-piggy-bank me-2"></i>PPK (jeśli dotyczy)</h6>
                    <div class="h5 mb-0"><span id="ppk-result">--</span> PLN</div>
                    <small class="text-muted">2% od brutto</small>
                  </div>
                </div>
              </div>

              <div class="mt-4">
                <h5 class="mb-3">Szczegółowy podział wynagrodzenia brutto-netto</h5>
                <div class="chart-container" style="height: 300px;">
                  <canvas id="bruttoNettoChart"></canvas>
                </div>
              </div>

              <div class="mt-4 d-flex justify-content-between flex-wrap gap-2">
                <button id="recalculate-btn" class="btn btn-outline-secondary">
                  <i class="fas fa-redo me-2"></i>Przelicz ponownie
                </button>
                <div>
                  <button id="share-results" class="btn btn-outline-primary me-2">
                    <i class="fas fa-share-alt me-2"></i>Udostępnij
                  </button>
                  <button id="print-results" class="btn btn-outline-success">
                    <i class="fas fa-print me-2"></i>Drukuj
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- 3W Content Section - Co to jest Kalkulator Brutto-Netto -->
<section class="py-5 bg-white" id="poradnik-brutto-netto">
  <div class="container">
    <div class="row">
      <div class="col-lg-8 mx-auto">
        <h2 class="fw-bold mb-4">Co to jest Kalkulator Brutto Netto?</h2>
        <p class="lead mb-4"><strong>Kalkulator brutto netto</strong> to specjalistyczne narzędzie online, które pozwala szybko i dokładnie obliczyć wynagrodzenie brutto na netto i odwrotnie. Nasz <strong>kalkulator wynagrodzeń brutto netto</strong> uwzględnia aktualne stawki składek ZUS, podatku dochodowego oraz Program Pracowniczych Planów Kapitałowych (PPK) obowiązujące w 2025 roku.</p>

        <p class="mb-4"><strong>Kalkulator brutto netto 2025</strong> umożliwia obliczenie:</p>
        <ul class="list-unstyled mb-4">
          <li class="mb-2"><i class="fas fa-check text-success me-2"></i><strong>Wynagrodzenia netto z brutto</strong> - ile otrzymasz na rękę</li>
          <li class="mb-2"><i class="fas fa-check text-success me-2"></i><strong>Wynagrodzenia brutto z netto</strong> - jaka musi być kwota brutto</li>
          <li class="mb-2"><i class="fas fa-check text-success me-2"></i><strong>Składek ZUS pracownika</strong> - emerytalna, rentowa, chorobowa</li>
          <li class="mb-2"><i class="fas fa-check text-success me-2"></i><strong>Podatku dochodowego</strong> - z uwzględnieniem progresji podatkowej</li>
          <li class="mb-2"><i class="fas fa-check text-success me-2"></i><strong>Składki PPK</strong> - jeśli uczestniczysz w programie</li>
        </ul>

        <div class="alert alert-info" role="alert">
          <h5 class="alert-heading"><i class="fas fa-info-circle me-2"></i>Ważne informacje</h5>
          <p class="mb-0">Nasz <strong>kalkulator brutto netto</strong> jest regularnie aktualizowany zgodnie z najnowszymi przepisami podatkowymi i składkami ZUS. Wszystkie obliczenia są wykonywane z uwzględnieniem aktualnych stawek obowiązujących w 2025 roku.</p>
        </div>

        <h2 class="fw-bold mb-4 mt-5">Dlaczego Warto Używać Naszego Kalkulatora Brutto Netto?</h2>
        <p class="mb-4">Nasz <strong>kalkulator wynagrodzeń brutto netto</strong> oferuje szereg korzyści:</p>

        <div class="row g-4 mb-4">
          <div class="col-md-6">
            <div class="d-flex">
              <div class="flex-shrink-0">
                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                  <i class="fas fa-clock"></i>
                </div>
              </div>
              <div class="flex-grow-1 ms-3">
                <h5 class="mb-2">Szybkość obliczeń</h5>
                <p class="mb-0">Natychmiastowe wyniki - oblicz brutto na netto w kilka sekund</p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="d-flex">
              <div class="flex-shrink-0">
                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                  <i class="fas fa-chart-line"></i>
                </div>
              </div>
              <div class="flex-grow-1 ms-3">
                <h5 class="mb-2">Wizualizacja wyników</h5>
                <p class="mb-0">Graficzny podział kosztów w formie przejrzystego wykresu</p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="d-flex">
              <div class="flex-shrink-0">
                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                  <i class="fas fa-shield-alt"></i>
                </div>
              </div>
              <div class="flex-grow-1 ms-3">
                <h5 class="mb-2">Aktualność przepisów</h5>
                <p class="mb-0">Uwzględnia najnowsze zmiany w składkach ZUS i podatkach 2025</p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="d-flex">
              <div class="flex-shrink-0">
                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                  <i class="fas fa-mobile-alt"></i>
                </div>
              </div>
              <div class="flex-grow-1 ms-3">
                <h5 class="mb-2">Responsywność</h5>
                <p class="mb-0">Działa na wszystkich urządzeniach - telefon, tablet, komputer</p>
              </div>
            </div>
          </div>
        </div>

        <h2 class="fw-bold mb-4 mt-5">Jak Obliczyć Wynagrodzenie Brutto na Netto?</h2>
        <p class="lead mb-4">Obliczenie wynagrodzenia brutto na netto wymaga uwzględnienia kilku składników:</p>

        <div class="card mb-4">
          <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>Formuła obliczania brutto na netto</h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <h6 class="text-primary">Krok 1: Składki ZUS pracownika</h6>
                <ul class="mb-3">
                  <li>Emerytalna: 9,76% od brutto</li>
                  <li>Rentowa: 1,5% od brutto</li>
                  <li>Chorobowa: 2,45% od brutto</li>
                  <li><strong>Razem: 13,71% od brutto</strong></li>
                </ul>
              </div>
              <div class="col-md-6">
                <h6 class="text-primary">Krok 2: Podstawa opodatkowania</h6>
                <p class="mb-3">Wynagrodzenie brutto minus składki ZUS pracownika</p>

                <h6 class="text-primary">Krok 3: Podatek dochodowy</h6>
                <ul class="mb-3">
                  <li>12% - do 120 000 PLN rocznie</li>
                  <li>32% - powyżej 120 000 PLN rocznie</li>
                  <li>Kwota wolna: 30 000 PLN rocznie</li>
                </ul>
              </div>
            </div>
            <div class="alert alert-success mt-3" role="alert">
              <strong>Wynagrodzenie netto = Brutto - Składki ZUS - Podatek dochodowy - PPK (jeśli dotyczy)</strong>
            </div>
          </div>
        </div>

        <h3 class="fw-bold mb-3">Przykład obliczenia brutto na netto</h3>
        <p class="mb-4">Zobaczmy jak obliczyć wynagrodzenie netto z kwoty brutto 5000 PLN:</p>

        <div class="row g-3 mb-4">
          <div class="col-md-6">
            <div class="card border-primary">
              <div class="card-header bg-primary text-white">
                <h6 class="mb-0">Dane wejściowe</h6>
              </div>
              <div class="card-body">
                <ul class="list-unstyled mb-0">
                  <li><strong>Wynagrodzenie brutto:</strong> 5000 PLN</li>
                  <li><strong>Typ umowy:</strong> Umowa o pracę</li>
                  <li><strong>Wiek:</strong> 26 lat</li>
                  <li><strong>PPK:</strong> Tak</li>
                </ul>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card border-success">
              <div class="card-header bg-success text-white">
                <h6 class="mb-0">Wyniki obliczeń</h6>
              </div>
              <div class="card-body">
                <ul class="list-unstyled mb-0">
                  <li><strong>Składki ZUS:</strong> 685,50 PLN</li>
                  <li><strong>Podatek dochodowy:</strong> 518,14 PLN</li>
                  <li><strong>PPK:</strong> 100,00 PLN</li>
                  <li><strong>Wynagrodzenie netto:</strong> 3696,36 PLN</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Comparison Table Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Porównanie Wynagrodzeń Brutto-Netto 2025</h2>
      <p class="lead">Zobacz jak różne kwoty brutto przekładają się na wynagrodzenie netto</p>
    </div>

    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="card shadow-sm">
          <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-table me-2"></i>Tabela Wynagrodzeń Brutto-Netto (Umowa o Pracę)</h5>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-striped table-hover mb-0">
                <thead class="table-dark">
                  <tr>
                    <th>Brutto (PLN)</th>
                    <th>Składki ZUS (13,71%)</th>
                    <th>Podatek dochodowy</th>
                    <th>PPK (2%)</th>
                    <th class="text-success"><strong>Netto (PLN)</strong></th>
                    <th>% Netto z Brutto</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td><strong>3000</strong></td>
                    <td>411,30</td>
                    <td>210,84</td>
                    <td>60,00</td>
                    <td class="text-success"><strong>2317,86</strong></td>
                    <td>77,3%</td>
                  </tr>
                  <tr>
                    <td><strong>4000</strong></td>
                    <td>548,40</td>
                    <td>330,19</td>
                    <td>80,00</td>
                    <td class="text-success"><strong>3041,41</strong></td>
                    <td>76,0%</td>
                  </tr>
                  <tr>
                    <td><strong>5000</strong></td>
                    <td>685,50</td>
                    <td>449,54</td>
                    <td>100,00</td>
                    <td class="text-success"><strong>3764,96</strong></td>
                    <td>75,3%</td>
                  </tr>
                  <tr>
                    <td><strong>6000</strong></td>
                    <td>822,60</td>
                    <td>568,89</td>
                    <td>120,00</td>
                    <td class="text-success"><strong>4488,51</strong></td>
                    <td>74,8%</td>
                  </tr>
                  <tr>
                    <td><strong>8000</strong></td>
                    <td>1096,80</td>
                    <td>807,58</td>
                    <td>160,00</td>
                    <td class="text-success"><strong>5935,62</strong></td>
                    <td>74,2%</td>
                  </tr>
                  <tr>
                    <td><strong>10000</strong></td>
                    <td>1371,00</td>
                    <td>1046,28</td>
                    <td>200,00</td>
                    <td class="text-success"><strong>7382,72</strong></td>
                    <td>73,8%</td>
                  </tr>
                  <tr>
                    <td><strong>15000</strong></td>
                    <td>2056,50</td>
                    <td>1642,62</td>
                    <td>300,00</td>
                    <td class="text-success"><strong>11000,88</strong></td>
                    <td>73,3%</td>
                  </tr>
                  <tr>
                    <td><strong>20000</strong></td>
                    <td>2742,00</td>
                    <td>2238,96</td>
                    <td>400,00</td>
                    <td class="text-success"><strong>14619,04</strong></td>
                    <td>73,1%</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="mt-3">
              <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                Obliczenia dla osoby w wieku 26 lat, uczestniczącej w PPK, bez ulg podatkowych.
                Rzeczywiste kwoty mogą się różnić w zależności od indywidualnej sytuacji.
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-5 bg-white">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Często Zadawane Pytania - Kalkulator Brutto Netto</h2>
      <p class="lead">Znajdź odpowiedzi na najczęściej zadawane pytania o <strong>kalkulator brutto netto</strong></p>
    </div>

    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="accordion" id="bruttoNettoFaqAccordion">
          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingBruttoOne">
              <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseBruttoOne" aria-expanded="true" aria-controls="collapseBruttoOne">
                Jak obliczyć wynagrodzenie brutto na netto?
              </button>
            </h3>
            <div id="collapseBruttoOne" class="accordion-collapse collapse show" aria-labelledby="headingBruttoOne" data-bs-parent="#bruttoNettoFaqAccordion">
              <div class="accordion-body">
                <p><strong>Aby obliczyć wynagrodzenie brutto na netto</strong>, należy od kwoty brutto odjąć składki ZUS pracownika (13,71%), podatek dochodowy oraz ewentualnie składkę PPK (2%). Nasz <strong>kalkulator brutto netto</strong> automatycznie wykonuje te obliczenia uwzględniając:</p>
                <ul>
                  <li>Składki emerytalne, rentowe i chorobowe ZUS</li>
                  <li>Podatek dochodowy z progresją podatkową</li>
                  <li>Kwotę wolną od podatku (30 000 PLN rocznie)</li>
                  <li>Składkę PPK jeśli uczestniczysz w programie</li>
                </ul>
              </div>
            </div>
          </div>

          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingBruttoTwo">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseBruttoTwo" aria-expanded="false" aria-controls="collapseBruttoTwo">
                Jakie są aktualne stawki składek ZUS w 2025 roku?
              </button>
            </h3>
            <div id="collapseBruttoTwo" class="accordion-collapse collapse" aria-labelledby="headingBruttoTwo" data-bs-parent="#bruttoNettoFaqAccordion">
              <div class="accordion-body">
                <p>W 2025 roku <strong>składki ZUS pracownika</strong> wynoszą łącznie <strong>13,71%</strong> od wynagrodzenia brutto:</p>
                <ul>
                  <li><strong>Składka emerytalna:</strong> 9,76% od brutto</li>
                  <li><strong>Składka rentowa:</strong> 1,5% od brutto</li>
                  <li><strong>Składka chorobowa:</strong> 2,45% od brutto</li>
                </ul>
                <p>Dodatkowo pracodawca płaci składki w wysokości około 19,52%, które nie wpływają na wynagrodzenie netto pracownika.</p>
              </div>
            </div>
          </div>

          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingBruttoThree">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseBruttoThree" aria-expanded="false" aria-controls="collapseBruttoThree">
                Ile wynosi podatek dochodowy od wynagrodzenia?
              </button>
            </h3>
            <div id="collapseBruttoThree" class="accordion-collapse collapse" aria-labelledby="headingBruttoThree" data-bs-parent="#bruttoNettoFaqAccordion">
              <div class="accordion-body">
                <p><strong>Podatek dochodowy od wynagrodzenia</strong> w 2025 roku jest naliczany progresywnie:</p>
                <ul>
                  <li><strong>12%</strong> - dla dochodów do 120 000 PLN rocznie</li>
                  <li><strong>32%</strong> - dla dochodów powyżej 120 000 PLN rocznie</li>
                </ul>
                <p>Kwota wolna od podatku wynosi <strong>30 000 PLN rocznie</strong> (2500 PLN miesięcznie). Podatek jest naliczany od podstawy opodatkowania, czyli wynagrodzenia brutto pomniejszonego o składki ZUS pracownika.</p>
              </div>
            </div>
          </div>

          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingBruttoFour">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseBruttoFour" aria-expanded="false" aria-controls="collapseBruttoFour">
                Czy PPK wpływa na wynagrodzenie netto?
              </button>
            </h3>
            <div id="collapseBruttoFour" class="accordion-collapse collapse" aria-labelledby="headingBruttoFour" data-bs-parent="#bruttoNettoFaqAccordion">
              <div class="accordion-body">
                <p><strong>Tak, PPK (Pracownicze Plany Kapitałowe) wpływają na wynagrodzenie netto.</strong> Jeśli uczestniczysz w PPK, od Twojego wynagrodzenia brutto jest odliczana składka w wysokości:</p>
                <ul>
                  <li><strong>2% podstawowej składki pracownika</strong> (obowiązkowa)</li>
                  <li><strong>Do 2% dodatkowej składki pracownika</strong> (dobrowolna)</li>
                </ul>
                <p>Nasz <strong>kalkulator brutto netto</strong> uwzględnia podstawową składkę PPK w wysokości 2% przy obliczaniu wynagrodzenia netto.</p>
              </div>
            </div>
          </div>

          <div class="accordion-item shadow-sm">
            <h3 class="accordion-header" id="headingBruttoFive">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseBruttoFive" aria-expanded="false" aria-controls="collapseBruttoFive">
                Jak obliczyć wynagrodzenie netto na brutto?
              </button>
            </h3>
            <div id="collapseBruttoFive" class="accordion-collapse collapse" aria-labelledby="headingBruttoFive" data-bs-parent="#bruttoNettoFaqAccordion">
              <div class="accordion-body">
                <p><strong>Obliczenie wynagrodzenia netto na brutto</strong> jest bardziej skomplikowane, ponieważ wymaga odwrotnych obliczeń. Nasz <strong>kalkulator brutto netto</strong> automatycznie wykonuje te obliczenia metodą iteracyjną, uwzględniając:</p>
                <ul>
                  <li>Progresywną skalę podatkową</li>
                  <li>Kwotę wolną od podatku</li>
                  <li>Składki ZUS od kwoty brutto</li>
                  <li>Składkę PPK jeśli dotyczy</li>
                </ul>
                <p>Wystarczy wybrać opcję "Netto" i wprowadzić żądaną kwotę na rękę - kalkulator obliczy wymaganą kwotę brutto.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-primary text-white">
  <div class="container">
    <div class="row py-3 justify-content-center text-center">
      <div class="col-lg-8">
        <h2 class="fw-bold mb-3">Oblicz swoje wynagrodzenie brutto-netto już teraz!</h2>
        <p class="lead mb-4">Skorzystaj z naszego darmowego <strong>kalkulatora brutto netto 2025</strong> i sprawdź dokładnie ile wynosi Twoje wynagrodzenie</p>
        <a href="#kalkulator-brutto-netto" class="btn btn-light btn-lg px-5 py-3">
          <i class="fas fa-calculator me-2"></i>Oblicz Brutto-Netto Teraz
        </a>
      </div>
    </div>
  </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
  // Brutto-Netto Calculator JavaScript
  document.addEventListener('DOMContentLoaded', function() {
    const bruttoNettoForm = document.getElementById('brutto-netto-calculator');
    const bruttoNettoResults = document.getElementById('brutto-netto-results');
    let bruttoNettoChart = null;

    // Polish tax and ZUS rates for 2025
    const taxRates = {
      employment: {
        zusEmployee: 0.1371, // 13.71% (emerytalna 9.76% + rentowa 1.5% + chorobowa 2.45%)
        zusEmployer: 0.1952, // 19.52% (emerytalna 9.76% + rentowa 6.5% + wypadkowa 1.67% + FP 2.45% + FGŚP 0.1%)
        taxThreshold: 120000, // Tax threshold for 2025
        taxRate1: 0.12, // 12% tax rate
        taxRate2: 0.32, // 32% tax rate
        taxFreeAmount: 30000 // Tax-free amount for 2025
      },
      mandate: {
        zusEmployee: 0.1371,
        zusEmployer: 0.0255, // Only FP + FGŚP for mandate contracts
        taxThreshold: 120000,
        taxRate1: 0.12,
        taxRate2: 0.32,
        taxFreeAmount: 30000
      },
      b2b: {
        vatRate: 0.23, // 23% VAT
        incomeTaxFlat: 0.19, // 19% flat tax for B2B
        socialInsurance: 0 // No ZUS for B2B (simplified)
      },
      task: {
        taxRate: 0.12, // 12% tax on task contracts
        taxFreeAmount: 200 // Monthly tax-free amount for task contracts
      }
    };

    // Calculate salary function
    function calculateBruttoNetto(amount, contractType, amountType, age, ppkEnabled) {
      const rates = taxRates[contractType];
      if (!rates) return null;

      let grossSalary, netSalary, zusCost, taxCost, ppkCost = 0;

      if (contractType === 'employment' || contractType === 'mandate') {
        if (amountType === 'gross') {
          grossSalary = amount;

          // Calculate ZUS
          zusCost = grossSalary * rates.zusEmployee;

          // Calculate taxable income
          const taxableIncome = grossSalary - zusCost;

          // Calculate income tax
          if (taxableIncome <= rates.taxFreeAmount / 12) { // Monthly tax-free amount
            taxCost = 0;
          } else {
            const taxableAmount = taxableIncome - (rates.taxFreeAmount / 12);
            const yearlyTaxableAmount = taxableAmount * 12;

            if (yearlyTaxableAmount <= rates.taxThreshold) {
              taxCost = taxableAmount * rates.taxRate1;
            } else {
              const monthlyThreshold = rates.taxThreshold / 12;
              if (taxableAmount <= monthlyThreshold) {
                taxCost = taxableAmount * rates.taxRate1;
              } else {
                taxCost = monthlyThreshold * rates.taxRate1 +
                         (taxableAmount - monthlyThreshold) * rates.taxRate2;
              }
            }
          }

          // Calculate PPK if enabled
          if (ppkEnabled) {
            ppkCost = grossSalary * 0.02; // 2% employee contribution
          }

          netSalary = grossSalary - zusCost - taxCost - ppkCost;

        } else { // net amount - reverse calculation
          netSalary = amount;

          // Iterative calculation for reverse brutto from netto
          let estimatedGross = amount * 1.45; // Initial estimate
          let iterations = 0;
          const maxIterations = 20;
          const tolerance = 0.01;

          while (iterations < maxIterations) {
            const testZus = estimatedGross * rates.zusEmployee;
            const testTaxableIncome = estimatedGross - testZus;

            let testTax = 0;
            if (testTaxableIncome > rates.taxFreeAmount / 12) {
              const testTaxableAmount = testTaxableIncome - (rates.taxFreeAmount / 12);
              const yearlyTaxableAmount = testTaxableAmount * 12;

              if (yearlyTaxableAmount <= rates.taxThreshold) {
                testTax = testTaxableAmount * rates.taxRate1;
              } else {
                const monthlyThreshold = rates.taxThreshold / 12;
                if (testTaxableAmount <= monthlyThreshold) {
                  testTax = testTaxableAmount * rates.taxRate1;
                } else {
                  testTax = monthlyThreshold * rates.taxRate1 +
                           (testTaxableAmount - monthlyThreshold) * rates.taxRate2;
                }
              }
            }

            const testPpk = ppkEnabled ? estimatedGross * 0.02 : 0;
            const calculatedNet = estimatedGross - testZus - testTax - testPpk;

            const difference = calculatedNet - netSalary;

            if (Math.abs(difference) < tolerance) {
              break;
            }

            // Adjust estimate
            estimatedGross = estimatedGross - difference * 0.8;
            iterations++;
          }

          grossSalary = estimatedGross;
          zusCost = grossSalary * rates.zusEmployee;

          const taxableIncome = grossSalary - zusCost;
          if (taxableIncome <= rates.taxFreeAmount / 12) {
            taxCost = 0;
          } else {
            const taxableAmount = taxableIncome - (rates.taxFreeAmount / 12);
            const yearlyTaxableAmount = taxableAmount * 12;

            if (yearlyTaxableAmount <= rates.taxThreshold) {
              taxCost = taxableAmount * rates.taxRate1;
            } else {
              const monthlyThreshold = rates.taxThreshold / 12;
              if (taxableAmount <= monthlyThreshold) {
                taxCost = taxableAmount * rates.taxRate1;
              } else {
                taxCost = monthlyThreshold * rates.taxRate1 +
                         (taxableAmount - monthlyThreshold) * rates.taxRate2;
              }
            }
          }

          if (ppkEnabled) {
            ppkCost = grossSalary * 0.02;
          }
        }

      } else if (contractType === 'b2b') {
        if (amountType === 'gross') {
          grossSalary = amount;
          zusCost = 0; // No ZUS for B2B
          taxCost = grossSalary * rates.incomeTaxFlat;
          netSalary = grossSalary - taxCost;
        } else {
          netSalary = amount;
          grossSalary = amount / (1 - rates.incomeTaxFlat);
          zusCost = 0;
          taxCost = grossSalary - netSalary;
        }

      } else if (contractType === 'task') {
        if (amountType === 'gross') {
          grossSalary = amount;
          zusCost = 0; // No ZUS for task contracts
          const taxableAmount = Math.max(0, grossSalary - rates.taxFreeAmount);
          taxCost = taxableAmount * rates.taxRate;
          netSalary = grossSalary - taxCost;
        } else {
          netSalary = amount;
          grossSalary = (amount + rates.taxFreeAmount) / (1 - rates.taxRate);
          zusCost = 0;
          taxCost = grossSalary - netSalary;
        }
      }

      return {
        gross: Math.round(grossSalary * 100) / 100,
        net: Math.round(netSalary * 100) / 100,
        zus: Math.round(zusCost * 100) / 100,
        tax: Math.round(taxCost * 100) / 100,
        ppk: Math.round(ppkCost * 100) / 100
      };
    }

    // Create salary breakdown chart
    function createBruttoNettoChart(data) {
      const ctx = document.getElementById('bruttoNettoChart').getContext('2d');

      if (bruttoNettoChart) {
        bruttoNettoChart.destroy();
      }

      bruttoNettoChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: ['Wynagrodzenie netto', 'Składki ZUS', 'Podatek dochodowy', 'PPK'],
          datasets: [{
            data: [data.net, data.zus, data.tax, data.ppk],
            backgroundColor: [
              '#28a745',
              '#ffc107',
              '#dc3545',
              '#6f42c1'
            ],
            borderWidth: 2,
            borderColor: '#fff'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                padding: 20,
                usePointStyle: true
              }
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  const label = context.label || '';
                  const value = context.parsed;
                  const total = context.dataset.data.reduce((a, b) => a + b, 0);
                  const percentage = ((value / total) * 100).toFixed(1);
                  return `${label}: ${value.toLocaleString('pl-PL')} PLN (${percentage}%)`;
                }
              }
            }
          }
        }
      });
    }

    // Form submission handler
    if (bruttoNettoForm) {
      bruttoNettoForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Get form values
        const contractType = document.getElementById('contract-type').value;
        const amount = parseFloat(document.getElementById('salary-amount').value);
        const amountType = document.querySelector('input[name="amount-type"]:checked').value;
        const age = parseInt(document.getElementById('age').value) || 26;
        const ppkEnabled = document.getElementById('ppk').checked;

        if (!contractType || !amount) {
          alert('Proszę wypełnić wszystkie wymagane pola.');
          return;
        }

        if (amount <= 0) {
          alert('Kwota musi być większa od zera.');
          return;
        }

        // Calculate salary
        const results = calculateBruttoNetto(amount, contractType, amountType, age, ppkEnabled);

        if (!results) {
          alert('Błąd w obliczeniach. Proszę spróbować ponownie.');
          return;
        }

        // Display results
        document.getElementById('gross-result').textContent = results.gross.toLocaleString('pl-PL');
        document.getElementById('net-result').textContent = results.net.toLocaleString('pl-PL');
        document.getElementById('zus-result').textContent = results.zus.toLocaleString('pl-PL');
        document.getElementById('tax-result').textContent = results.tax.toLocaleString('pl-PL');
        document.getElementById('ppk-result').textContent = results.ppk.toLocaleString('pl-PL');

        // Create chart
        createBruttoNettoChart(results);

        // Show results section
        bruttoNettoResults.style.display = 'block';

        // Scroll to results
        bruttoNettoResults.scrollIntoView({ behavior: 'smooth' });
      });
    }

    // Recalculate button handler
    const recalculateBtn = document.getElementById('recalculate-btn');
    if (recalculateBtn) {
      recalculateBtn.addEventListener('click', function() {
        bruttoNettoResults.style.display = 'none';
        bruttoNettoForm.reset();
        // Reset to default values
        document.getElementById('contract-type').value = 'employment';
        document.getElementById('age').value = '26';
        document.querySelector('input[name="amount-type"][value="gross"]').checked = true;

        if (bruttoNettoChart) {
          bruttoNettoChart.destroy();
          bruttoNettoChart = null;
        }
        // Scroll back to calculator
        document.getElementById('kalkulator-brutto-netto').scrollIntoView({ behavior: 'smooth' });
      });
    }

    // Print button handler
    const printResultsBtn = document.getElementById('print-results');
    if (printResultsBtn) {
      printResultsBtn.addEventListener('click', function() {
        window.print();
      });
    }

    // Share button handler
    const shareResultsBtn = document.getElementById('share-results');
    if (shareResultsBtn) {
      shareResultsBtn.addEventListener('click', function() {
        const gross = document.getElementById('gross-result').textContent;
        const net = document.getElementById('net-result').textContent;
        const shareText = `Obliczałem wynagrodzenie w kalkulatorze brutto-netto: ${gross} PLN brutto = ${net} PLN netto`;

        // Try to share via Web Share API if available
        if (navigator.share) {
          navigator.share({
            title: 'Moje wyniki z kalkulatora brutto-netto',
            text: shareText,
            url: window.location.href
          });
        } else {
          // Fallback: copy to clipboard
          navigator.clipboard.writeText(shareText + ' - ' + window.location.href)
            .then(() => {
              alert('Wyniki skopiowane do schowka!');
            })
            .catch(() => {
              // Fallback for older browsers
              const textArea = document.createElement('textarea');
              textArea.value = shareText + ' - ' + window.location.href;
              document.body.appendChild(textArea);
              textArea.select();
              document.execCommand('copy');
              document.body.removeChild(textArea);
              alert('Wyniki skopiowane do schowka!');
            });
        }
      });
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });

    // Auto-calculate on input change (optional feature)
    const salaryAmountInput = document.getElementById('salary-amount');
    const contractTypeSelect = document.getElementById('contract-type');
    const amountTypeRadios = document.querySelectorAll('input[name="amount-type"]');
    const ppkCheckbox = document.getElementById('ppk');

    function autoCalculate() {
      const amount = parseFloat(salaryAmountInput.value);
      if (amount > 0 && contractTypeSelect.value) {
        // Auto-submit form after a short delay
        setTimeout(() => {
          if (parseFloat(salaryAmountInput.value) === amount && contractTypeSelect.value) {
            bruttoNettoForm.dispatchEvent(new Event('submit'));
          }
        }, 1000);
      }
    }

    // Add event listeners for auto-calculation (optional)
    // salaryAmountInput.addEventListener('input', autoCalculate);
    // contractTypeSelect.addEventListener('change', autoCalculate);
    // amountTypeRadios.forEach(radio => radio.addEventListener('change', autoCalculate));
    // ppkCheckbox.addEventListener('change', autoCalculate);
  });
</script>
{% endblock %}
