import os
import shutil
import sys

from app import app, babel


def set_language(lang_code):
    def select_locale():
        return lang_code

    babel.locale_selector_func = select_locale


def ensure_dir(path):
    """确保目录存在"""
    if not os.path.exists(path):
        os.makedirs(path)


def save_page(url, content, build_dir):
    """保存页面内容到文件"""
    if url.endswith('/'):
        url = url + 'index.html'
    elif not url.endswith('.html'):
        url = url + '/index.html'

    # 移除开头的斜杠
    if url.startswith('/'):
        url = url[1:]

    # 构建完整路径
    file_path = os.path.join(build_dir, url)

    # 确保目录存在
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    # 保存内容
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)


def has_parameters(rule):
    """检查路由规则是否包含参数"""
    return '<' in rule.rule


try:
    # 设置Flask配置
    app.config['BABEL_DEFAULT_LOCALE'] = 'fr'
    app.config['SERVER_NAME'] = 'kalkulatorwynagrodzen.blog'

    # 设置英语为默认语言
    set_language('fr')

    # 创建build目录
    build_dir = 'build'
    if os.path.exists(build_dir):
        shutil.rmtree(build_dir)
    os.makedirs(build_dir)

    # 生成页面
    with app.test_client() as client:
        # 遍历所有路由并生成页面
        for rule in app.url_map.iter_rules():
            if rule.endpoint != 'static':  # 跳过静态文件路由
                # 处理静态路由
                url = rule.rule
                try:
                    response = client.get(url)
                    if response.status_code == 200:
                        save_page(url.lstrip('/'), response.data.decode('utf-8'), build_dir)
                except Exception as e:
                    print(f"Error generating {url}: {e}", file=sys.stderr)

    # 复制静态文件
    if os.path.exists('static'):
        shutil.copytree('static', os.path.join(build_dir, 'static'))

    # 复制其他静态文件
    for file in ['robots.txt', 'sitemap.xml', 'favicon.ico', 'llms.txt', 'llms-full.txt']:
        src = os.path.join('static', file)
        if os.path.exists(src):
            shutil.copy2(src, os.path.join(build_dir, file))

    # 生成404页面
    response = client.get('/non-existent-page')  # 触发404错误
    save_page('404.html', response.data.decode('utf-8'), build_dir)

    print("Static files generation completed!")
except Exception as e:
    print(f"Error during build process: {e}", file=sys.stderr)
    sys.exit(1)
