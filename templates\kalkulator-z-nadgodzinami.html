{% extends 'base.html' %}

{% block head %}
<!-- Chart.js for overtime salary breakdown visualization -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block title %}Kalkulator Wynagrodzeń z Nadgodzinami 2025 | Oblicz Dodatek za Nadgodziny{% endblock %}

{% block description %}✅ Kalkulator wynagrodzeń z nadgodzinami 2025 - oblicz dodatek za nadgodziny 50% i 100%, wynagrodzenie brutto netto z nadgodzinami. Darmowy kalkulator!{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active" aria-current="page">Kalkulator z Nadgodzinami</li>
{% endblock %}

{% block schema_data %}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": ["WebApplication", "Calculator"],
  "name": "Kalk<PERSON> Wynagrodzeń z Nadgodzinami 2025",
  "url": "https://kalkulatorwynagrodzen.blog/kalkulator-z-nadgodzinami",
  "description": "Darmowy kalkulator wynagrodzeń z nadgodzinami 2025 - oblicz dodatek za nadgodziny 50% i 100%, wynagrodzenie brutto netto z nadgodzinami",
  "applicationCategory": "FinanceApplication",
  "operatingSystem": "All",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "PLN"
  },
  "author": {
    "@type": "Organization",
    "name": "Kalkulator Wynagrodzeń",
    "url": "https://kalkulatorwynagrodzen.blog/"
  },
  "potentialAction": {
    "@type": "UseAction",
    "target": "https://kalkulatorwynagrodzen.blog/kalkulator-z-nadgodzinami#kalkulator",
    "object": {
      "@type": "WebApplication",
      "name": "Kalkulator Wynagrodzeń z Nadgodzinami"
    }
  }
}
</script>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "Jak obliczyć dodatek za nadgodziny?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Dodatek za nadgodziny wynosi 50% stawki godzinowej za pierwsze 2 godziny nadliczbowe dziennie i 100% za kolejne godziny. Nasz kalkulator automatycznie oblicza te dodatki."
      }
    },
    {
      "@type": "Question",
      "name": "Czy nadgodziny podlegają składkom ZUS?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Tak, wynagrodzenie za nadgodziny podlega składkom ZUS i podatkowi dochodowemu na takich samych zasadach jak podstawowe wynagrodzenie."
      }
    }
  ]
}
</script>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section bg-primary text-white text-center py-5" id="hero">
  <div class="container">
    <h1 class="display-4 fw-bold mb-4">Kalkulator Wynagrodzeń z Nadgodzinami 2025</h1>
    <p class="lead mb-5">Oblicz wynagrodzenie z dodatkiem za nadgodziny 50% i 100%. Darmowy <strong>kalkulator wynagrodzeń z nadgodzinami</strong> uwzględniający składki ZUS i podatek.</p>
    <div class="d-flex justify-content-center gap-3 flex-wrap">
      <a href="#kalkulator" class="btn btn-light btn-lg px-4 py-2">
        <i class="fas fa-clock me-2"></i>Oblicz z Nadgodzinami
      </a>
      <a href="#poradnik" class="btn btn-outline-light btn-lg px-4 py-2">
        <i class="fas fa-book me-2"></i>Jak Liczyć Nadgodziny
      </a>
    </div>
  </div>
</section>

<!-- Kalkulator z Nadgodzinami Section -->
<section id="kalkulator" class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Kalkulator Wynagrodzeń z Nadgodzinami 2025</h2>
      <p class="lead">Nasz <strong>kalkulator wynagrodzeń z nadgodzinami</strong> pozwala obliczyć wynagrodzenie z dodatkami za nadgodziny 50% i 100%.</p>
    </div>

    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="card shadow-sm">
          <div class="card-body p-4">
            <form id="overtime-calculator">
              <div class="row g-3 mb-4">
                <div class="col-md-6">
                  <label for="base-salary" class="form-label fw-bold">Wynagrodzenie podstawowe (brutto)</label>
                  <div class="input-group">
                    <input type="number" id="base-salary" class="form-control" placeholder="5000" min="0" step="0.01" required>
                    <span class="input-group-text">PLN</span>
                  </div>
                </div>

                <div class="col-md-6">
                  <label for="working-hours" class="form-label fw-bold">Godziny pracy miesięcznie</label>
                  <div class="input-group">
                    <input type="number" id="working-hours" class="form-control" placeholder="168" min="1" max="300" value="168" required>
                    <span class="input-group-text">godz.</span>
                  </div>
                </div>
              </div>

              <div class="row g-3 mb-4">
                <div class="col-md-4">
                  <label for="overtime-50" class="form-label fw-bold">Nadgodziny 50%</label>
                  <div class="input-group">
                    <input type="number" id="overtime-50" class="form-control" placeholder="0" min="0" max="100" value="0">
                    <span class="input-group-text">godz.</span>
                  </div>
                  <small class="text-muted">Pierwsze 2 godz. dziennie</small>
                </div>

                <div class="col-md-4">
                  <label for="overtime-100" class="form-label fw-bold">Nadgodziny 100%</label>
                  <div class="input-group">
                    <input type="number" id="overtime-100" class="form-control" placeholder="0" min="0" max="100" value="0">
                    <span class="input-group-text">godz.</span>
                  </div>
                  <small class="text-muted">Powyżej 2 godz. dziennie</small>
                </div>

                <div class="col-md-4">
                  <label for="age-overtime" class="form-label fw-bold">Wiek</label>
                  <div class="input-group">
                    <input type="number" id="age-overtime" class="form-control" placeholder="26" min="18" max="100" value="26">
                    <span class="input-group-text">lat</span>
                  </div>
                </div>
              </div>

              <div class="row g-3 mb-4">
                <div class="col-md-6">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="ppk-overtime" value="ppk">
                    <label class="form-check-label fw-bold" for="ppk-overtime">
                      Uczestniczę w PPK
                    </label>
                  </div>
                </div>
              </div>

              <div class="text-center">
                <button type="submit" class="btn btn-primary btn-lg px-5 py-3">
                  <i class="fas fa-clock me-2"></i>OBLICZ Z NADGODZINAMI
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- Results Section -->
        <div id="overtime-results" class="mt-4" style="display: none;">
          <div class="card border-success">
            <div class="card-header bg-success text-white">
              <h4 class="mb-0"><i class="fas fa-chart-line me-2"></i>Wyniki Kalkulacji z Nadgodzinami</h4>
            </div>
            <div class="card-body">
              <div class="row g-4">
                <div class="col-md-6">
                  <div class="result-item p-3 bg-light rounded">
                    <h5 class="text-primary mb-2">Łączne wynagrodzenie brutto</h5>
                    <div class="h3 text-dark mb-0"><span id="total-gross-result">--</span> PLN</div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="result-item p-3 bg-light rounded">
                    <h5 class="text-success mb-2">Łączne wynagrodzenie netto</h5>
                    <div class="h3 text-dark mb-0"><span id="total-net-result">--</span> PLN</div>
                  </div>
                </div>
              </div>

              <div class="row g-4 mt-2">
                <div class="col-md-3">
                  <div class="result-item p-3 border rounded">
                    <h6 class="text-muted mb-2">Podstawa brutto</h6>
                    <div class="h5 mb-0"><span id="base-gross-result">--</span> PLN</div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="result-item p-3 border rounded">
                    <h6 class="text-muted mb-2">Dodatek za nadgodziny</h6>
                    <div class="h5 mb-0"><span id="overtime-bonus-result">--</span> PLN</div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="result-item p-3 border rounded">
                    <h6 class="text-muted mb-2">Składki ZUS</h6>
                    <div class="h5 mb-0"><span id="zus-overtime-result">--</span> PLN</div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="result-item p-3 border rounded">
                    <h6 class="text-muted mb-2">Podatek</h6>
                    <div class="h5 mb-0"><span id="tax-overtime-result">--</span> PLN</div>
                  </div>
                </div>
              </div>

              <div class="mt-4">
                <h5 class="mb-3">Szczegółowy podział wynagrodzeń z nadgodzinami</h5>
                <div class="chart-container" style="height: 300px;">
                  <canvas id="overtimeChart"></canvas>
                </div>
              </div>

              <div class="mt-4">
                <h5 class="mb-3">Tabela szczegółowych obliczeń</h5>
                <div class="table-responsive">
                  <table class="table table-striped">
                    <thead>
                      <tr>
                        <th>Składnik wynagrodzenia</th>
                        <th>Godziny</th>
                        <th>Stawka godzinowa</th>
                        <th>Kwota brutto</th>
                      </tr>
                    </thead>
                    <tbody id="calculation-details">
                      <!-- Dynamically filled -->
                    </tbody>
                  </table>
                </div>
              </div>

              <div class="mt-4 d-flex justify-content-between flex-wrap gap-2">
                <button id="recalculate-overtime-btn" class="btn btn-outline-secondary">
                  <i class="fas fa-redo me-2"></i>Przelicz ponownie
                </button>
                <div>
                  <button id="share-overtime-results" class="btn btn-outline-primary me-2">
                    <i class="fas fa-share-alt me-2"></i>Udostępnij
                  </button>
                  <button id="print-overtime-results" class="btn btn-outline-success">
                    <i class="fas fa-print me-2"></i>Drukuj
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- 3W Content Section - Co to jest Kalkulator z Nadgodzinami -->
<section class="py-5 bg-white" id="poradnik">
  <div class="container">
    <div class="row">
      <div class="col-lg-8 mx-auto">
        <h2 class="fw-bold mb-4">Co to jest Kalkulator Wynagrodzeń z Nadgodzinami?</h2>
        <p class="lead mb-4"><strong>Kalkulator wynagrodzeń z nadgodzinami</strong> to specjalistyczne narzędzie online, które pozwala precyzyjnie obliczyć wynagrodzenie z uwzględnieniem dodatków za nadgodziny zgodnie z polskim prawem pracy. Nasz <strong>kalkulator wynagrodzeń z nadgodzinami 2025</strong> automatycznie uwzględnia stawki 50% i 100% za nadgodziny oraz wszystkie obowiązujące składki i podatki.</p>

        <p class="mb-4">Zgodnie z Kodeksem Pracy, <strong>kalkulator wynagrodzeń z nadgodzinami</strong> oblicza:</p>
        <ul class="list-unstyled mb-4">
          <li class="mb-2"><i class="fas fa-check text-success me-2"></i><strong>Dodatek 50%</strong> - za pierwsze 2 godziny nadliczbowe dziennie</li>
          <li class="mb-2"><i class="fas fa-check text-success me-2"></i><strong>Dodatek 100%</strong> - za kolejne godziny nadliczbowe powyżej 2 godzin dziennie</li>
          <li class="mb-2"><i class="fas fa-check text-success me-2"></i><strong>Składki ZUS</strong> - od całości wynagrodzenia z nadgodzinami</li>
          <li class="mb-2"><i class="fas fa-check text-success me-2"></i><strong>Podatek dochodowy</strong> - z progresją podatkową</li>
          <li class="mb-2"><i class="fas fa-check text-success me-2"></i><strong>PPK</strong> - jeśli pracownik uczestniczy w programie</li>
        </ul>

        <div class="alert alert-info">
          <h5 class="alert-heading"><i class="fas fa-info-circle me-2"></i>Ważne informacje prawne</h5>
          <p class="mb-0"><strong>Kalkulator wynagrodzeń z nadgodzinami</strong> bazuje na przepisach Kodeksu Pracy. Maksymalny czas pracy z nadgodzinami nie może przekroczyć 48 godzin tygodniowo w okresie rozliczeniowym.</p>
        </div>

        <h2 class="fw-bold mb-4 mt-5">Dlaczego Warto Używać Naszego Kalkulatora z Nadgodzinami?</h2>
        <p class="mb-4">Nasz <strong>kalkulator wynagrodzeń z nadgodzinami</strong> oferuje unikalne korzyści:</p>

        <div class="row g-4 mb-4">
          <div class="col-md-6">
            <div class="d-flex">
              <div class="flex-shrink-0">
                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                  <i class="fas fa-gavel"></i>
                </div>
              </div>
              <div class="flex-grow-1 ms-3">
                <h5 class="mb-2">Zgodność z prawem</h5>
                <p class="mb-0">Obliczenia zgodne z Kodeksem Pracy i aktualnymi przepisami 2025</p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="d-flex">
              <div class="flex-shrink-0">
                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                  <i class="fas fa-calculator"></i>
                </div>
              </div>
              <div class="flex-grow-1 ms-3">
                <h5 class="mb-2">Automatyczne obliczenia</h5>
                <p class="mb-0">Automatyczny podział na nadgodziny 50% i 100%</p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="d-flex">
              <div class="flex-shrink-0">
                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                  <i class="fas fa-chart-pie"></i>
                </div>
              </div>
              <div class="flex-grow-1 ms-3">
                <h5 class="mb-2">Wizualizacja wyników</h5>
                <p class="mb-0">Wykresy i tabele pokazujące szczegółowy podział kosztów</p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="d-flex">
              <div class="flex-shrink-0">
                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                  <i class="fas fa-clock"></i>
                </div>
              </div>
              <div class="flex-grow-1 ms-3">
                <h5 class="mb-2">Oszczędność czasu</h5>
                <p class="mb-0">Błyskawiczne obliczenia zamiast żmudnych kalkulacji ręcznych</p>
              </div>
            </div>
          </div>
        </div>

        <h2 class="fw-bold mb-4 mt-5">Jak Obliczyć Nadgodziny - Kompletny Poradnik</h2>
        <p class="lead mb-4">Korzystanie z <strong>kalkulatora wynagrodzeń z nadgodzinami</strong> jest proste, ale warto znać podstawy prawne:</p>

        <div class="row g-4">
          <div class="col-md-6">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body p-4">
                <div class="text-primary mb-3">
                  <i class="fas fa-money-bill-wave fa-2x"></i>
                </div>
                <h5 class="card-title">1. Ustal stawkę godzinową</h5>
                <p class="card-text">Podziel wynagrodzenie podstawowe przez liczbę godzin pracy w miesiącu</p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body p-4">
                <div class="text-primary mb-3">
                  <i class="fas fa-clock fa-2x"></i>
                </div>
                <h5 class="card-title">2. Policz nadgodziny</h5>
                <p class="card-text">Pierwsze 2 godz. dziennie = 50%, kolejne = 100% stawki podstawowej</p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body p-4">
                <div class="text-primary mb-3">
                  <i class="fas fa-plus fa-2x"></i>
                </div>
                <h5 class="card-title">3. Dodaj do podstawy</h5>
                <p class="card-text">Suma: wynagrodzenie podstawowe + dodatki za nadgodziny</p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body p-4">
                <div class="text-primary mb-3">
                  <i class="fas fa-percentage fa-2x"></i>
                </div>
                <h5 class="card-title">4. Oblicz składki i podatek</h5>
                <p class="card-text">ZUS i podatek od całości wynagrodzenia z nadgodzinami</p>
              </div>
            </div>
          </div>
        </div>

        <div class="mt-5 p-4 bg-light rounded">
          <h5 class="text-primary mb-3"><i class="fas fa-lightbulb me-2"></i>Przykład obliczenia nadgodzin</h5>
          <p class="mb-3">Pracownik z wynagrodzeniem 5000 PLN brutto (168 godz./miesiąc) pracuje 10 nadgodzin:</p>
          <ul class="mb-0">
            <li>Stawka godzinowa: 5000 ÷ 168 = 29,76 PLN</li>
            <li>Nadgodziny 50% (8 godz.): 8 × 29,76 × 1,5 = 357,12 PLN</li>
            <li>Nadgodziny 100% (2 godz.): 2 × 29,76 × 2 = 119,04 PLN</li>
            <li><strong>Łączne wynagrodzenie brutto: 5000 + 357,12 + 119,04 = 5476,16 PLN</strong></li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Legal Basis Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Podstawy Prawne Nadgodzin w Polsce 2025</h2>
      <p class="lead">Nasz <strong>kalkulator wynagrodzeń z nadgodzinami</strong> bazuje na aktualnych przepisach Kodeksu Pracy</p>
    </div>

    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="card shadow-sm">
          <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-gavel me-2"></i>Stawki Dodatków za Nadgodziny 2025</h5>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-striped mb-0">
                <thead>
                  <tr>
                    <th>Rodzaj nadgodzin</th>
                    <th>Dodatek</th>
                    <th>Podstawa prawna</th>
                    <th>Opis</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td><strong>Pierwsze 2 godziny dziennie</strong></td>
                    <td><span class="badge bg-success">50%</span></td>
                    <td>Art. 151¹ § 1 KP</td>
                    <td>Dodatek za nadgodziny w dni robocze</td>
                  </tr>
                  <tr>
                    <td><strong>Powyżej 2 godzin dziennie</strong></td>
                    <td><span class="badge bg-warning">100%</span></td>
                    <td>Art. 151¹ § 1 KP</td>
                    <td>Zwiększony dodatek za nadgodziny</td>
                  </tr>
                  <tr>
                    <td><strong>Nadgodziny w niedziele</strong></td>
                    <td><span class="badge bg-warning">100%</span></td>
                    <td>Art. 151¹ § 2 KP</td>
                    <td>Praca w niedziele i święta</td>
                  </tr>
                  <tr>
                    <td><strong>Nadgodziny nocne</strong></td>
                    <td><span class="badge bg-info">20% + dodatek</span></td>
                    <td>Art. 151⁵ KP</td>
                    <td>Praca w godzinach 22:00-6:00</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <div class="row g-4 mt-4">
          <div class="col-md-6">
            <div class="card h-100 border-warning">
              <div class="card-header bg-warning text-dark">
                <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Limity nadgodzin</h6>
              </div>
              <div class="card-body">
                <ul class="mb-0">
                  <li>Maksymalnie 8 godzin tygodniowo</li>
                  <li>Maksymalnie 150 godzin rocznie</li>
                  <li>Nie więcej niż 48 godzin tygodniowo łącznie</li>
                  <li>Obowiązkowy odpoczynek 11 godzin</li>
                </ul>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card h-100 border-info">
              <div class="card-header bg-info text-white">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Składki i podatki</h6>
              </div>
              <div class="card-body">
                <ul class="mb-0">
                  <li>Nadgodziny podlegają składkom ZUS</li>
                  <li>Podlegają podatkowi dochodowemu</li>
                  <li>Wliczają się do podstawy PPK</li>
                  <li>Uwzględniane w progresji podatkowej</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-5 bg-white">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Często Zadawane Pytania o Nadgodziny</h2>
      <p class="lead">Najważniejsze informacje o <strong>kalkulatorze wynagrodzeń z nadgodzinami</strong></p>
    </div>

    <div class="row justify-content-center">
      <div class="col-lg-8">
        <div class="accordion" id="faqAccordion">
          <div class="accordion-item">
            <h3 class="accordion-header" id="faq1">
              <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                Jak obliczyć dodatek za nadgodziny?
              </button>
            </h3>
            <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                <strong>Kalkulator wynagrodzeń z nadgodzinami</strong> automatycznie oblicza dodatki: 50% za pierwsze 2 godziny nadliczbowe dziennie i 100% za kolejne godziny. Podstawą jest stawka godzinowa z wynagrodzenia podstawowego.
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header" id="faq2">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                Czy nadgodziny podlegają składkom ZUS?
              </button>
            </h3>
            <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                Tak, wynagrodzenie za nadgodziny podlega składkom ZUS i podatkowi dochodowemu na takich samych zasadach jak podstawowe wynagrodzenie. <strong>Kalkulator wynagrodzeń z nadgodzinami</strong> uwzględnia wszystkie obowiązkowe składki.
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header" id="faq3">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                Ile maksymalnie można pracować nadgodzin?
              </button>
            </h3>
            <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                Zgodnie z Kodeksem Pracy, maksymalnie można pracować 8 godzin nadliczbowych tygodniowo i 150 godzin rocznie. Łączny czas pracy nie może przekroczyć 48 godzin tygodniowo.
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header" id="faq4">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4">
                Czy kalkulator uwzględnia pracę w niedziele?
              </button>
            </h3>
            <div id="collapse4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                Nasz <strong>kalkulator wynagrodzeń z nadgodzinami</strong> obecnie oblicza standardowe nadgodziny w dni robocze. Praca w niedziele i święta wymaga dodatkowego dodatku 100% zgodnie z art. 151¹ § 2 KP.
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h3 class="accordion-header" id="faq5">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse5">
                Jak dokładny jest kalkulator nadgodzin?
              </button>
            </h3>
            <div id="collapse5" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                <strong>Kalkulator wynagrodzeń z nadgodzinami</strong> bazuje na aktualnych przepisach Kodeksu Pracy i stawkach składek ZUS na 2025 rok. Obliczenia są precyzyjne i zgodne z obowiązującym prawem.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('overtime-calculator');
    const resultsDiv = document.getElementById('overtime-results');

    // ZUS rates for 2025
    const zusRates = {
        emerytalna: 0.0976,
        rentowa: 0.015,
        chorobowa: 0.0245,
        wypadkowa: 0.0167,
        fp: 0.0245,
        fgsp: 0.001
    };

    const totalZusEmployee = zusRates.emerytalna + zusRates.rentowa + zusRates.chorobowa;
    const ppkRate = 0.02; // 2% employee contribution

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        calculateOvertimeSalary();
    });

    function calculateOvertimeSalary() {
        const baseSalary = parseFloat(document.getElementById('base-salary').value) || 0;
        const workingHours = parseFloat(document.getElementById('working-hours').value) || 168;
        const overtime50 = parseFloat(document.getElementById('overtime-50').value) || 0;
        const overtime100 = parseFloat(document.getElementById('overtime-100').value) || 0;
        const age = parseInt(document.getElementById('age-overtime').value) || 26;
        const hasPpk = document.getElementById('ppk-overtime').checked;

        if (baseSalary <= 0) {
            alert('Proszę wprowadzić prawidłowe wynagrodzenie podstawowe');
            return;
        }

        // Calculate hourly rate
        const hourlyRate = baseSalary / workingHours;

        // Calculate overtime bonuses
        const overtime50Bonus = overtime50 * hourlyRate * 0.5;
        const overtime100Bonus = overtime100 * hourlyRate * 1.0;
        const totalOvertimeBonus = overtime50Bonus + overtime100Bonus;

        // Total gross salary
        const totalGross = baseSalary + totalOvertimeBonus;

        // Calculate ZUS contributions
        const zusContributions = totalGross * totalZusEmployee;

        // Calculate tax base (gross - ZUS employee contributions)
        const taxBase = totalGross - zusContributions;

        // Calculate income tax (simplified - 12% for most cases)
        const taxRate = age < 26 ? 0 : 0.12; // Young people exemption
        const incomeTax = Math.max(0, (taxBase - 30000/12) * taxRate); // Monthly tax-free amount

        // Calculate PPK contribution
        const ppkContribution = hasPpk ? totalGross * ppkRate : 0;

        // Calculate net salary
        const totalNet = totalGross - zusContributions - incomeTax - ppkContribution;

        // Display results
        displayResults({
            baseSalary,
            totalOvertimeBonus,
            totalGross,
            totalNet,
            zusContributions,
            incomeTax,
            ppkContribution,
            hourlyRate,
            overtime50,
            overtime100,
            overtime50Bonus,
            overtime100Bonus
        });

        // Show results section
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    }

    function displayResults(data) {
        document.getElementById('base-gross-result').textContent = data.baseSalary.toFixed(2);
        document.getElementById('overtime-bonus-result').textContent = data.totalOvertimeBonus.toFixed(2);
        document.getElementById('total-gross-result').textContent = data.totalGross.toFixed(2);
        document.getElementById('total-net-result').textContent = data.totalNet.toFixed(2);
        document.getElementById('zus-overtime-result').textContent = data.zusContributions.toFixed(2);
        document.getElementById('tax-overtime-result').textContent = data.incomeTax.toFixed(2);

        // Fill calculation details table
        const tableBody = document.getElementById('calculation-details');
        tableBody.innerHTML = `
            <tr>
                <td><strong>Wynagrodzenie podstawowe</strong></td>
                <td>-</td>
                <td>-</td>
                <td>${data.baseSalary.toFixed(2)} PLN</td>
            </tr>
            <tr>
                <td>Nadgodziny 50%</td>
                <td>${data.overtime50}</td>
                <td>${(data.hourlyRate * 1.5).toFixed(2)} PLN</td>
                <td>${data.overtime50Bonus.toFixed(2)} PLN</td>
            </tr>
            <tr>
                <td>Nadgodziny 100%</td>
                <td>${data.overtime100}</td>
                <td>${(data.hourlyRate * 2).toFixed(2)} PLN</td>
                <td>${data.overtime100Bonus.toFixed(2)} PLN</td>
            </tr>
            <tr class="table-success">
                <td><strong>ŁĄCZNIE BRUTTO</strong></td>
                <td><strong>-</strong></td>
                <td><strong>-</strong></td>
                <td><strong>${data.totalGross.toFixed(2)} PLN</strong></td>
            </tr>
        `;

        // Create chart
        createOvertimeChart(data);
    }

    function createOvertimeChart(data) {
        const ctx = document.getElementById('overtimeChart').getContext('2d');

        // Destroy existing chart if it exists
        if (window.overtimeChartInstance) {
            window.overtimeChartInstance.destroy();
        }

        window.overtimeChartInstance = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Wynagrodzenie podstawowe', 'Nadgodziny 50%', 'Nadgodziny 100%', 'Składki ZUS', 'Podatek'],
                datasets: [{
                    data: [
                        data.baseSalary,
                        data.overtime50Bonus,
                        data.overtime100Bonus,
                        data.zusContributions,
                        data.incomeTax
                    ],
                    backgroundColor: [
                        '#0d6efd',
                        '#198754',
                        '#ffc107',
                        '#dc3545',
                        '#6c757d'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.parsed.toFixed(2) + ' PLN';
                            }
                        }
                    }
                }
            }
        });
    }

    // Recalculate button
    document.getElementById('recalculate-overtime-btn').addEventListener('click', function() {
        resultsDiv.style.display = 'none';
        document.getElementById('kalkulator').scrollIntoView({ behavior: 'smooth' });
    });

    // Share results
    document.getElementById('share-overtime-results').addEventListener('click', function() {
        if (navigator.share) {
            navigator.share({
                title: 'Wyniki kalkulatora wynagrodzeń z nadgodzinami',
                text: 'Sprawdź moje obliczenia wynagrodzeń z nadgodzinami',
                url: window.location.href
            });
        } else {
            // Fallback - copy to clipboard
            navigator.clipboard.writeText(window.location.href);
            alert('Link skopiowany do schowka!');
        }
    });

    // Print results
    document.getElementById('print-overtime-results').addEventListener('click', function() {
        window.print();
    });
});
</script>
{% endblock %}

{% endblock %}
