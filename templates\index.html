{% extends 'base.html' %}

{% block head %}
<!-- Chart.js for salary breakdown visualization -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block title %}Kalkulator Wynagrodzeń 2025 | <PERSON><PERSON><PERSON> | Darmowy{% endblock %}

{% block description %}✅ Kalkulator wynagrodzeń 2025 - oblicz pensję brutto netto, PPK, składki <PERSON>. Darmowy kalkulator płac dla wszystkich typów umów. Sprawdź teraz!{% endblock %}

{% block schema_data %}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": ["WebApplication", "Calculator"],
  "name": "Kalkulator Wynagrodzeń 2025",
  "url": "https://kalkulatorwynagrodzen.blog/",
  "description": "Darmowy kalkulator wynagrodzeń 2025 - oblicz pensję brutto netto, sk<PERSON><PERSON><PERSON>, PPK dla wszystkich typów umów",
  "applicationCategory": "FinanceApplication",
  "operatingSystem": "All",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "PLN"
  },
  "author": {
    "@type": "Organization",
    "name": "Kalkulator Wynagrodzeń",
    "url": "https://kalkulatorwynagrodzen.blog/"
  },
  "potentialAction": {
    "@type": "UseAction",
    "target": "https://kalkulatorwynagrodzen.blog/#kalkulator",
    "object": {
      "@type": "WebApplication",
      "name": "Kalkulator Wynagrodzeń"
    }
  }
}
</script>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "Czy kalkulator wynagrodzeń jest darmowy?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Tak, nasz kalkulator wynagrodzeń jest całkowicie darmowy i nie wymaga rejestracji."
      }
    },
    {
      "@type": "Question",
      "name": "Jak dokładny jest kalkulator wynagrodzeń?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Nasz kalkulator oferuje wysoką dokładność, bazując na aktualnych przepisach podatkowych i składkach ZUS na 2025 rok."
      }
    }
  ]
}
</script>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section bg-primary text-white text-center py-5" id="hero">
  <div class="container">
    <h1 class="display-4 fw-bold mb-4">Kalkulator Wynagrodzeń 2025 - Oblicz Pensję Brutto Netto</h1>
    <p class="lead mb-5">Darmowy kalkulator wynagrodzeń 2025 - oblicz pensję brutto netto, składki ZUS i PPK dla wszystkich typów umów</p>
    <div class="d-flex justify-content-center gap-3 flex-wrap">
      <a href="#kalkulator" class="btn btn-light btn-lg px-4 py-2">
        <i class="fas fa-calculator me-2"></i>Oblicz Wynagrodzenie
      </a>
      <a href="#poradnik" class="btn btn-outline-light btn-lg px-4 py-2">
        <i class="fas fa-book me-2"></i>Jak Używać
      </a>
    </div>
  </div>
</section>

<!-- Kalkulator Wynagrodzeń Section -->
<section id="kalkulator" class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Kalkulator Wynagrodzeń 2025 - Oblicz Pensję</h2>
      <p class="lead">Nasz <strong>kalkulator wynagrodzeń</strong> pozwala szybko obliczyć wynagrodzenie brutto netto dla różnych typów umów w 2025 roku.</p>
    </div>

    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="card shadow-sm">
          <div class="card-body p-4">
            <form id="salary-calculator">
              <div class="row g-3 mb-4">
                <div class="col-md-6">
                  <label for="contract-type" class="form-label fw-bold">Typ umowy</label>
                  <select id="contract-type" class="form-select" required>
                    <option value="">Wybierz typ umowy</option>
                    <option value="employment">Umowa o pracę</option>
                    <option value="mandate">Umowa zlecenie</option>
                    <option value="b2b">Umowa B2B</option>
                    <option value="task">Umowa o dzieło</option>
                  </select>
                </div>

                <div class="col-md-6">
                  <label for="tax-year" class="form-label fw-bold">Rok podatkowy</label>
                  <select id="tax-year" class="form-select">
                    <option value="2025" selected>2025</option>
                  </select>
                </div>
              </div>

              <div class="row g-3 mb-4">
                <div class="col-md-6">
                  <label for="salary-amount" class="form-label fw-bold">Kwota</label>
                  <div class="input-group">
                    <input type="number" id="salary-amount" class="form-control" placeholder="Wprowadź kwotę" min="0" step="0.01" required>
                    <span class="input-group-text">PLN</span>
                  </div>
                </div>

                <div class="col-md-6">
                  <label class="form-label fw-bold">Typ kwoty</label>
                  <div class="d-flex gap-3 mt-2">
                    <div class="form-check">
                      <input class="form-check-input" type="radio" name="amount-type" id="gross" value="gross" checked>
                      <label class="form-check-label" for="gross">Brutto</label>
                    </div>
                    <div class="form-check">
                      <input class="form-check-input" type="radio" name="amount-type" id="net" value="net">
                      <label class="form-check-label" for="net">Netto</label>
                    </div>
                  </div>
                </div>
              </div>

              <div class="row g-3 mb-4">
                <div class="col-md-4">
                  <label for="age" class="form-label fw-bold">Wiek</label>
                  <div class="input-group">
                    <input type="number" id="age" class="form-control" placeholder="26" min="18" max="100">
                    <span class="input-group-text">lat</span>
                  </div>
                </div>

                <div class="col-md-4">
                  <label for="location" class="form-label fw-bold">Miejsce pracy</label>
                  <select id="location" class="form-select">
                    <option value="other">Inne miasta</option>
                    <option value="warszawa">Warszawa</option>
                    <option value="krakow">Kraków</option>
                    <option value="gdansk">Gdańsk</option>
                    <option value="wroclaw">Wrocław</option>
                  </select>
                </div>

                <div class="col-md-4">
                  <div class="form-check mt-4">
                    <input class="form-check-input" type="checkbox" id="ppk" value="ppk">
                    <label class="form-check-label fw-bold" for="ppk">
                      Uczestniczę w PPK
                    </label>
                  </div>
                </div>
              </div>

              <div class="text-center">
                <button type="submit" class="btn btn-primary btn-lg px-5 py-3">
                  <i class="fas fa-calculator me-2"></i>OBLICZ WYNAGRODZENIE
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- Results Section -->
        <div id="salary-results" class="mt-4" style="display: none;">
          <div class="card border-success">
            <div class="card-header bg-success text-white">
              <h4 class="mb-0"><i class="fas fa-chart-line me-2"></i>Wyniki Kalkulacji Wynagrodzeń</h4>
            </div>
            <div class="card-body">
              <div class="row g-4">
                <div class="col-md-6">
                  <div class="result-item p-3 bg-light rounded">
                    <h5 class="text-primary mb-2">Wynagrodzenie brutto</h5>
                    <div class="h3 text-dark mb-0"><span id="gross-result">--</span> PLN</div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="result-item p-3 bg-light rounded">
                    <h5 class="text-success mb-2">Wynagrodzenie netto</h5>
                    <div class="h3 text-dark mb-0"><span id="net-result">--</span> PLN</div>
                  </div>
                </div>
              </div>

              <div class="row g-4 mt-2">
                <div class="col-md-4">
                  <div class="result-item p-3 border rounded">
                    <h6 class="text-muted mb-2">Składki ZUS</h6>
                    <div class="h5 mb-0"><span id="zus-result">--</span> PLN</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="result-item p-3 border rounded">
                    <h6 class="text-muted mb-2">Podatek dochodowy</h6>
                    <div class="h5 mb-0"><span id="tax-result">--</span> PLN</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="result-item p-3 border rounded">
                    <h6 class="text-muted mb-2">PPK (jeśli dotyczy)</h6>
                    <div class="h5 mb-0"><span id="ppk-result">--</span> PLN</div>
                  </div>
                </div>
              </div>

              <div class="mt-4">
                <h5 class="mb-3">Szczegółowy podział kosztów</h5>
                <div class="chart-container" style="height: 300px;">
                  <canvas id="salaryChart"></canvas>
                </div>
              </div>

              <div class="mt-4 d-flex justify-content-between flex-wrap gap-2">
                <button id="recalculate-btn" class="btn btn-outline-secondary">
                  <i class="fas fa-redo me-2"></i>Przelicz ponownie
                </button>
                <div>
                  <button id="share-results" class="btn btn-outline-primary me-2">
                    <i class="fas fa-share-alt me-2"></i>Udostępnij
                  </button>
                  <button id="print-results" class="btn btn-outline-success">
                    <i class="fas fa-print me-2"></i>Drukuj
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- 3W Content Section - Co to jest Kalkulator Wynagrodzeń -->
<section class="py-5 bg-white" id="poradnik">
  <div class="container">
    <div class="row">
      <div class="col-lg-8 mx-auto">
        <h2 class="fw-bold mb-4">Co to jest Kalkulator Wynagrodzeń?</h2>
        <p class="lead mb-4"><strong>Kalkulator wynagrodzeń</strong> to darmowe narzędzie online, które pozwala szybko i dokładnie obliczyć wynagrodzenie brutto netto dla różnych typów umów w 2025 roku. Nasz <strong>kalkulator wynagrodzeń</strong> uwzględnia aktualne stawki składek ZUS, podatku dochodowego oraz Program Pracowniczych Planów Kapitałowych (PPK).</p>

        <p class="mb-4"><strong>Kalkulator wynagrodzeń 2025</strong> obsługuje:</p>
        <ul class="list-unstyled mb-4">
          <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Umowy o pracę (etat pełny i pół etatu)</li>
          <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Umowy zlecenie</li>
          <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Umowy B2B (działalność gospodarcza)</li>
          <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Umowy o dzieło</li>
        </ul>

        <h2 class="fw-bold mb-4 mt-5">Dlaczego Warto Używać Naszego Kalkulatora Wynagrodzeń?</h2>
        <p class="mb-4"><strong>Kalkulator wynagrodzeń brutto netto</strong> oferuje:</p>

        <div class="row g-4 mb-4">
          <div class="col-md-6">
            <div class="d-flex">
              <div class="flex-shrink-0">
                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                  <span class="fw-bold">1</span>
                </div>
              </div>
              <div class="flex-grow-1 ms-3">
                <h5 class="mb-2">Aktualność</h5>
                <p class="mb-0">Uwzględnia zmiany w przepisach na 2025 rok</p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="d-flex">
              <div class="flex-shrink-0">
                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                  <span class="fw-bold">2</span>
                </div>
              </div>
              <div class="flex-grow-1 ms-3">
                <h5 class="mb-2">Dokładność</h5>
                <p class="mb-0">Precyzyjne obliczenia zgodne z obowiązującymi stawkami</p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="d-flex">
              <div class="flex-shrink-0">
                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                  <span class="fw-bold">3</span>
                </div>
              </div>
              <div class="flex-grow-1 ms-3">
                <h5 class="mb-2">Prostota</h5>
                <p class="mb-0">Intuicyjny interfejs dostępny dla każdego</p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="d-flex">
              <div class="flex-shrink-0">
                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                  <span class="fw-bold">4</span>
                </div>
              </div>
              <div class="flex-grow-1 ms-3">
                <h5 class="mb-2">Kompleksowość</h5>
                <p class="mb-0">Obsługa wszystkich typów umów</p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="d-flex">
              <div class="flex-shrink-0">
                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                  <span class="fw-bold">5</span>
                </div>
              </div>
              <div class="flex-grow-1 ms-3">
                <h5 class="mb-2">Darmowość</h5>
                <p class="mb-0">Bezpłatne korzystanie bez ograniczeń</p>
              </div>
            </div>
          </div>
        </div>

        <p class="mb-4"><strong>Kalkulator wynagrodzeń pracodawcy</strong> pomaga również:</p>
        <ul class="mb-4">
          <li>Planować budżet na wynagrodzenia</li>
          <li>Porównywać koszty różnych typów umów</li>
          <li>Optymalizować strukturę wynagrodzeń</li>
        </ul>
      </div>
    </div>
  </div>
</section>

<!-- How to Use Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="row">
      <div class="col-lg-8 mx-auto">
        <h2 class="fw-bold mb-4">Jak Używać Kalkulatora Wynagrodzeń?</h2>
        <p class="lead mb-4">Korzystanie z <strong>kalkulatora wynagrodzeń</strong> jest bardzo proste:</p>

        <div class="row g-4">
          <div class="col-md-6">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body p-4">
                <div class="text-primary mb-3">
                  <i class="fas fa-list-alt fa-2x"></i>
                </div>
                <h5 class="card-title">1. Wybierz typ umowy</h5>
                <p class="card-text">Umowa o pracę, zlecenie, B2B lub dzieło</p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body p-4">
                <div class="text-primary mb-3">
                  <i class="fas fa-edit fa-2x"></i>
                </div>
                <h5 class="card-title">2. Wprowadź kwotę</h5>
                <p class="card-text">Podaj wynagrodzenie brutto lub netto</p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body p-4">
                <div class="text-primary mb-3">
                  <i class="fas fa-user-edit fa-2x"></i>
                </div>
                <h5 class="card-title">3. Uzupełnij dane</h5>
                <p class="card-text">Wiek, miejsce pracy, uczestnictwo w PPK</p>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card h-100 border-0 shadow-sm">
              <div class="card-body p-4">
                <div class="text-primary mb-3">
                  <i class="fas fa-calculator fa-2x"></i>
                </div>
                <h5 class="card-title">4. Kliknij "Oblicz"</h5>
                <p class="card-text">Otrzymaj szczegółowe wyniki</p>
              </div>
            </div>
          </div>
        </div>

        <div class="mt-5 p-4 bg-white rounded shadow-sm">
          <h5 class="text-primary mb-3"><i class="fas fa-clock me-2"></i>Kalkulator Wynagrodzeń z Nadgodzinami</h5>
          <p class="mb-0"><strong>Kalkulator wynagrodzeń z nadgodzinami</strong> automatycznie uwzględni podstawowe wynagrodzenie, dodatki za nadgodziny (50% i 100%), składki od całości wynagrodzenia oraz podatek dochodowy z progresją.</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Popular Calculators Section -->
<section class="py-5 bg-white">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Popularne Kalkulatory Wynagrodzeń</h2>
      <p class="lead">Wybierz kalkulator dostosowany do Twojego typu umowy</p>
    </div>

    <div class="row g-4">
      <div class="col-md-6 col-lg-4">
        <div class="card h-100 shadow-sm border-0">
          <div class="card-body p-4">
            <div class="text-primary mb-3">
              <i class="fas fa-briefcase fa-2x"></i>
            </div>
            <h3 class="h5 mb-3">Kalkulator Wynagrodzeń B2B</h3>
            <p class="card-text">Specjalistyczny <strong>kalkulator wynagrodzeń B2B</strong> dla przedsiębiorców rozliczających się na zasadach ogólnych lub ryczałcie.</p>
            <div class="mt-auto pt-3">
              <a href="#" class="btn btn-outline-primary d-block">Oblicz B2B</a>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-6 col-lg-4">
        <div class="card h-100 shadow-sm border-0">
          <div class="card-body p-4">
            <div class="text-primary mb-3">
              <i class="fas fa-file-contract fa-2x"></i>
            </div>
            <h3 class="h5 mb-3">Kalkulator Wynagrodzeń KUP</h3>
            <p class="card-text"><strong>Kalkulator wynagrodzeń KUP</strong> (Kodeks Pracy) dla standardowych umów o pracę z uwzględnieniem wszystkich składek.</p>
            <div class="mt-auto pt-3">
              <a href="#" class="btn btn-outline-primary d-block">Oblicz KUP</a>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-6 col-lg-4">
        <div class="card h-100 shadow-sm border-0">
          <div class="card-body p-4">
            <div class="text-primary mb-3">
              <i class="fas fa-chart-line fa-2x"></i>
            </div>
            <h3 class="h5 mb-3">Kalkulator Wynagrodzeń Sedlak</h3>
            <p class="card-text">Porównaj wyniki z <strong>kalkulatorem wynagrodzeń Sedlak</strong> - nasz kalkulator oferuje identyczną dokładność.</p>
            <div class="mt-auto pt-3">
              <a href="#" class="btn btn-outline-primary d-block">Porównaj</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Current Rates 2025 Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Aktualne Stawki 2025</h2>
      <p class="lead"><strong>Kalkulator wynagrodzeń 2025 brutto netto</strong> uwzględnia najnowsze stawki składek i podatków</p>
    </div>

    <div class="row justify-content-center">
      <div class="col-lg-8">
        <div class="card shadow-sm">
          <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-table me-2"></i>Składki ZUS 2025</h5>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-striped mb-0">
                <thead>
                  <tr>
                    <th>Składka</th>
                    <th>Stawka 2025</th>
                    <th>Opis</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td><strong>Emerytalna</strong></td>
                    <td><span class="badge bg-primary">9,76%</span></td>
                    <td>Składka na ubezpieczenie emerytalne</td>
                  </tr>
                  <tr>
                    <td><strong>Rentowa</strong></td>
                    <td><span class="badge bg-primary">1,5%</span></td>
                    <td>Składka na ubezpieczenie rentowe</td>
                  </tr>
                  <tr>
                    <td><strong>Chorobowa</strong></td>
                    <td><span class="badge bg-primary">2,45%</span></td>
                    <td>Składka na ubezpieczenie chorobowe</td>
                  </tr>
                  <tr>
                    <td><strong>Wypadkowa</strong></td>
                    <td><span class="badge bg-warning text-dark">0,67% - 3,33%</span></td>
                    <td>Zależna od branży i ryzyka</td>
                  </tr>
                  <tr>
                    <td><strong>FP</strong></td>
                    <td><span class="badge bg-primary">2,45%</span></td>
                    <td>Fundusz Pracy</td>
                  </tr>
                  <tr>
                    <td><strong>FGŚP</strong></td>
                    <td><span class="badge bg-primary">0,1%</span></td>
                    <td>Fundusz Gwarantowanych Świadczeń Pracowniczych</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-5 bg-white">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Często Zadawane Pytania</h2>
      <p class="lead">Znajdź odpowiedzi na najczęściej zadawane pytania o <strong>kalkulator wynagrodzeń</strong></p>
    </div>

    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="accordion" id="faqAccordion">
          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingOne">
              <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                Czy kalkulator wynagrodzeń jest darmowy?
              </button>
            </h3>
            <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                <p><strong>Tak, nasz kalkulator wynagrodzeń jest całkowicie darmowy i nie wymaga rejestracji.</strong> Możesz korzystać z niego bez ograniczeń, obliczając wynagrodzenia dla różnych typów umów. Nasz <strong>kalkulator wynagrodzeń 2025</strong> jest dostępny 24/7 i zawsze aktualizowany zgodnie z najnowszymi przepisami.</p>
              </div>
            </div>
          </div>

          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingTwo">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                Jak dokładny jest kalkulator wynagrodzeń gofin?
              </button>
            </h3>
            <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                <p>Nasz <strong>kalkulator wynagrodzeń</strong> oferuje taką samą dokładność jak <strong>kalkulator wynagrodzeń gofin</strong>, bazując na aktualnych przepisach podatkowych i składkach ZUS. Wszystkie obliczenia są przeprowadzane zgodnie z obowiązującymi stawkami na 2025 rok, uwzględniając:</p>
                <ul>
                  <li>Aktualne stawki składek ZUS</li>
                  <li>Podatek dochodowy z progresją podatkową</li>
                  <li>Składki na PPK (jeśli dotyczy)</li>
                  <li>Ulgi podatkowe i kwoty wolne od podatku</li>
                </ul>
              </div>
            </div>
          </div>

          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingThree">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                Czy mogę obliczyć wynagrodzenie pół etatu?
              </button>
            </h3>
            <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                <p><strong>Tak, kalkulator wynagrodzenia pół etatu</strong> automatycznie przelicza składki i podatki proporcjonalnie do wymiaru etatu. Nasz <strong>kalkulator wynagrodzeń</strong> obsługuje wszystkie wymiary etatu:</p>
                <ul>
                  <li>Pełny etat (1.0)</li>
                  <li>Pół etatu (0.5)</li>
                  <li>Ćwierć etatu (0.25)</li>
                  <li>Dowolny wymiar etatu</li>
                </ul>
                <p>Wystarczy wprowadzić odpowiednią kwotę wynagrodzenia, a kalkulator automatycznie obliczy wszystkie składki i podatki.</p>
              </div>
            </div>
          </div>

          <div class="accordion-item shadow-sm">
            <h3 class="accordion-header" id="headingFour">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                Jakie typy umów obsługuje kalkulator wynagrodzeń?
              </button>
            </h3>
            <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="headingFour" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                <p>Nasz <strong>kalkulator wynagrodzeń brutto netto</strong> obsługuje wszystkie popularne typy umów:</p>
                <ul>
                  <li><strong>Umowa o pracę</strong> - z pełnymi składkami ZUS i podatkiem dochodowym</li>
                  <li><strong>Umowa zlecenie</strong> - z odpowiednimi składkami społecznymi</li>
                  <li><strong>Umowa B2B</strong> - dla działalności gospodarczej</li>
                  <li><strong>Umowa o dzieło</strong> - z podatkiem dochodowym</li>
                </ul>
                <p>Każdy typ umowy ma swoje specyficzne zasady rozliczania, które są automatycznie uwzględniane w kalkulacji.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

<!-- CTA Section -->
<section class="py-5 bg-primary text-white">
  <div class="container">
    <div class="row py-3 justify-content-center text-center">
      <div class="col-lg-8">
        <h2 class="fw-bold mb-3">Gotowy do obliczenia swojego wynagrodzenia?</h2>
        <p class="lead mb-4">Skorzystaj z naszego darmowego <strong>kalkulatora wynagrodzeń 2025</strong> i sprawdź, ile wynosi Twoja pensja netto</p>
        <a href="#kalkulator" class="btn btn-light btn-lg px-5 py-3">
          <i class="fas fa-calculator me-2"></i>Oblicz Wynagrodzenie Teraz
        </a>
      </div>
    </div>
  </div>
</section>



<!-- FAQ Section -->
<section class="py-5 bg-white">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Questions fréquentes sur le bac</h2>
      <p class="lead">Trouvez les réponses aux questions les plus courantes sur le baccalauréat</p>
    </div>
    
    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="accordion" id="faqAccordion">
          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingOne">
              <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                Comment calculer ma moyenne au bac ?
              </button>
            </h3>
            <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                <p>La moyenne au baccalauréat est calculée en fonction des notes obtenues aux différentes épreuves, pondérées par leurs coefficients respectifs. Notre simulateur bac vous permet d'effectuer ce calcul automatiquement.</p>
                <p>La formule est la suivante : <strong>Moyenne = Somme(Note × Coefficient) / Somme(Coefficient)</strong></p>
              </div>
            </div>
          </div>
          
          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingTwo">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                Quelles sont les mentions possibles au bac ?
              </button>
            </h3>
            <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                <p>Les mentions au baccalauréat sont attribuées en fonction de votre moyenne générale :</p>
                <ul>
                  <li><strong>Assez Bien</strong> : Moyenne entre 12/20 et 13,99/20</li>
                  <li><strong>Bien</strong> : Moyenne entre 14/20 et 15,99/20</li>
                  <li><strong>Très Bien</strong> : Moyenne égale ou supérieure à 16/20</li>
                </ul>
                <p>Notre simulateur vous indique la mention que vous pourriez obtenir en fonction de vos notes.</p>
              </div>
            </div>
          </div>
          
          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingThree">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                Comment fonctionnent les points d'avance au bac ?
              </button>
            </h3>
            <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                <p>Les points d'avance correspondent aux points au-dessus de la moyenne de 10/20 multipliés par le total des coefficients. Ils peuvent compenser des notes plus faibles dans certaines matières.</p>
                <p>Par exemple, si vous avez 12/20 de moyenne générale avec un total de coefficients de 100, vous avez (12-10) × 100 = 200 points d'avance.</p>
              </div>
            </div>
          </div>
          
          <div class="accordion-item shadow-sm">
            <h3 class="accordion-header" id="headingFour">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                Où trouver les sujets du bac des années précédentes ?
              </button>
            </h3>
            <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="headingFour" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                <p>Vous pouvez consulter notre section <a href="#" class="text-decoration-none">Sujets Bac</a> qui regroupe les annales des différentes filières, ou visiter le site officiel de l'Éducation Nationale.</p>
                <p>Les annales sont un excellent moyen de se préparer aux épreuves et de comprendre le format des questions posées.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-primary text-white">
  <div class="container">
    <div class="row py-3 justify-content-center text-center">
      <div class="col-lg-8">
        <h2 class="fw-bold mb-3">Prêt à calculer votre moyenne au bac ?</h2>
        <p class="lead mb-4">Utilisez notre simulateur gratuit pour estimer votre moyenne et découvrir votre mention potentielle</p>
        <a href="#simulateur" class="btn btn-light btn-lg px-4 py-2">
          <i class="fas fa-calculator me-2"></i>Calculer ma moyenne maintenant
        </a>
      </div>
    </div>
  </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
  // Polish Salary Calculator JavaScript
  document.addEventListener('DOMContentLoaded', function() {
    const salaryForm = document.getElementById('salary-calculator');
    const salaryResults = document.getElementById('salary-results');
    let salaryChart = null;

    // Polish tax and ZUS rates for 2025
    const taxRates = {
      employment: {
        zusEmployee: 0.1371, // 13.71% (emerytalna 9.76% + rentowa 1.5% + chorobowa 2.45%)
        zusEmployer: 0.1952, // 19.52% (emerytalna 9.76% + rentowa 6.5% + wypadkowa 1.67% + FP 2.45% + FGŚP 0.1%)
        taxThreshold: 120000, // Tax threshold for 2025
        taxRate1: 0.12, // 12% tax rate
        taxRate2: 0.32, // 32% tax rate
        taxFreeAmount: 30000 // Tax-free amount for 2025
      },
      mandate: {
        zusEmployee: 0.1371,
        zusEmployer: 0.0255, // Only FP + FGŚP for mandate contracts
        taxThreshold: 120000,
        taxRate1: 0.12,
        taxRate2: 0.32,
        taxFreeAmount: 30000
      },
      b2b: {
        vatRate: 0.23, // 23% VAT
        incomeTaxFlat: 0.19, // 19% flat tax for B2B
        socialInsurance: 0 // No ZUS for B2B (simplified)
      },
      task: {
        taxRate: 0.12, // 12% tax on task contracts
        taxFreeAmount: 200 // Monthly tax-free amount for task contracts
      }
    };

    // Calculate salary function
    function calculateSalary(amount, contractType, amountType, age, ppkEnabled) {
      const rates = taxRates[contractType];
      if (!rates) return null;

      let grossSalary, netSalary, zusCost, taxCost, ppkCost = 0;

      if (contractType === 'employment' || contractType === 'mandate') {
        if (amountType === 'gross') {
          grossSalary = amount;

          // Calculate ZUS
          zusCost = grossSalary * rates.zusEmployee;

          // Calculate taxable income
          const taxableIncome = grossSalary - zusCost;

          // Calculate income tax
          if (taxableIncome <= rates.taxFreeAmount) {
            taxCost = 0;
          } else {
            const taxableAmount = taxableIncome - rates.taxFreeAmount;
            if (taxableAmount <= rates.taxThreshold) {
              taxCost = taxableAmount * rates.taxRate1;
            } else {
              taxCost = rates.taxThreshold * rates.taxRate1 +
                       (taxableAmount - rates.taxThreshold) * rates.taxRate2;
            }
          }

          // Calculate PPK if enabled
          if (ppkEnabled) {
            ppkCost = grossSalary * 0.02; // 2% employee contribution
          }

          netSalary = grossSalary - zusCost - taxCost - ppkCost;

        } else { // net amount
          // This is a simplified reverse calculation
          // In reality, this requires iterative calculation
          netSalary = amount;
          grossSalary = amount * 1.4; // Approximate multiplier
          zusCost = grossSalary * rates.zusEmployee;
          taxCost = (grossSalary - zusCost - rates.taxFreeAmount) * rates.taxRate1;
          if (ppkEnabled) {
            ppkCost = grossSalary * 0.02;
          }
        }

      } else if (contractType === 'b2b') {
        if (amountType === 'gross') {
          grossSalary = amount;
          zusCost = 0; // No ZUS for B2B
          taxCost = grossSalary * rates.incomeTaxFlat;
          netSalary = grossSalary - taxCost;
        } else {
          netSalary = amount;
          grossSalary = amount / (1 - rates.incomeTaxFlat);
          zusCost = 0;
          taxCost = grossSalary - netSalary;
        }

      } else if (contractType === 'task') {
        if (amountType === 'gross') {
          grossSalary = amount;
          zusCost = 0; // No ZUS for task contracts
          const taxableAmount = Math.max(0, grossSalary - rates.taxFreeAmount);
          taxCost = taxableAmount * rates.taxRate;
          netSalary = grossSalary - taxCost;
        } else {
          netSalary = amount;
          grossSalary = (amount + rates.taxFreeAmount) / (1 - rates.taxRate);
          zusCost = 0;
          taxCost = grossSalary - netSalary;
        }
      }

      return {
        gross: Math.round(grossSalary * 100) / 100,
        net: Math.round(netSalary * 100) / 100,
        zus: Math.round(zusCost * 100) / 100,
        tax: Math.round(taxCost * 100) / 100,
        ppk: Math.round(ppkCost * 100) / 100
      };
    }

    // Create salary breakdown chart
    function createSalaryChart(data) {
      const ctx = document.getElementById('salaryChart').getContext('2d');

      if (salaryChart) {
        salaryChart.destroy();
      }

      salaryChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: ['Wynagrodzenie netto', 'Składki ZUS', 'Podatek dochodowy', 'PPK'],
          datasets: [{
            data: [data.net, data.zus, data.tax, data.ppk],
            backgroundColor: [
              '#28a745',
              '#ffc107',
              '#dc3545',
              '#6f42c1'
            ],
            borderWidth: 2,
            borderColor: '#fff'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                padding: 20,
                usePointStyle: true
              }
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  const label = context.label || '';
                  const value = context.parsed;
                  const total = context.dataset.data.reduce((a, b) => a + b, 0);
                  const percentage = ((value / total) * 100).toFixed(1);
                  return `${label}: ${value.toLocaleString('pl-PL')} PLN (${percentage}%)`;
                }
              }
            }
          }
        }
      });
    }

    // Form submission handler
    if (salaryForm) {
      salaryForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Get form values
        const contractType = document.getElementById('contract-type').value;
        const amount = parseFloat(document.getElementById('salary-amount').value);
        const amountType = document.querySelector('input[name="amount-type"]:checked').value;
        const age = parseInt(document.getElementById('age').value) || 26;
        const ppkEnabled = document.getElementById('ppk').checked;

        if (!contractType || !amount) {
          alert('Proszę wypełnić wszystkie wymagane pola.');
          return;
        }

        // Calculate salary
        const results = calculateSalary(amount, contractType, amountType, age, ppkEnabled);

        if (!results) {
          alert('Błąd w obliczeniach. Proszę spróbować ponownie.');
          return;
        }

        // Display results
        document.getElementById('gross-result').textContent = results.gross.toLocaleString('pl-PL');
        document.getElementById('net-result').textContent = results.net.toLocaleString('pl-PL');
        document.getElementById('zus-result').textContent = results.zus.toLocaleString('pl-PL');
        document.getElementById('tax-result').textContent = results.tax.toLocaleString('pl-PL');
        document.getElementById('ppk-result').textContent = results.ppk.toLocaleString('pl-PL');

        // Create chart
        createSalaryChart(results);

        // Show results section
        salaryResults.style.display = 'block';

        // Scroll to results
        salaryResults.scrollIntoView({ behavior: 'smooth' });
      });
    }

    // Recalculate button handler
    const recalculateBtn = document.getElementById('recalculate-btn');
    if (recalculateBtn) {
      recalculateBtn.addEventListener('click', function() {
        salaryResults.style.display = 'none';
        salaryForm.reset();
        if (salaryChart) {
          salaryChart.destroy();
          salaryChart = null;
        }
        // Scroll back to calculator
        document.getElementById('kalkulator').scrollIntoView({ behavior: 'smooth' });
      });
    }

    // Print button handler
    const printResultsBtn = document.getElementById('print-results');
    if (printResultsBtn) {
      printResultsBtn.addEventListener('click', function() {
        window.print();
      });
    }

    // Share button handler
    const shareResultsBtn = document.getElementById('share-results');
    if (shareResultsBtn) {
      shareResultsBtn.addEventListener('click', function() {
        const gross = document.getElementById('gross-result').textContent;
        const net = document.getElementById('net-result').textContent;
        const shareText = `Obliczałem wynagrodzenie w kalkulatorze wynagrodzeń: ${gross} PLN brutto = ${net} PLN netto`;

        // Try to share via Web Share API if available
        if (navigator.share) {
          navigator.share({
            title: 'Moje wyniki z kalkulatora wynagrodzeń',
            text: shareText,
            url: window.location.href
          });
        } else {
          // Fallback: copy to clipboard
          navigator.clipboard.writeText(shareText + ' - ' + window.location.href)
            .then(() => {
              alert('Wyniki skopiowane do schowka!');
            })
            .catch(() => {
              // Fallback for older browsers
              const textArea = document.createElement('textarea');
              textArea.value = shareText + ' - ' + window.location.href;
              document.body.appendChild(textArea);
              textArea.select();
              document.execCommand('copy');
              document.body.removeChild(textArea);
              alert('Wyniki skopiowane do schowka!');
            });
        }
      });
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });
  });
</script>
{% endblock %}